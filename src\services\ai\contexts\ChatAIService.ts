/**
 * Chat AI Service
 * 
 * خدمة الذكاء الاصطناعي المتخصصة في المحادثات التفاعلية
 */

import { AIService } from '../core/AIService';
import { PromptTemplateService } from '../utils/PromptTemplateService';
import { ResponseFormatterService } from '../utils/ResponseFormatterService';
import { 
  ChatAIRequest, 
  ChatAIResponse, 
  AIRequest, 
  AIMessage,
  StreamingResponse 
} from '../types';
import { Logger } from '@/core/error/Logger';

interface ConversationContext {
  personality: string;
  conversationHistory: AIMessage[];
  userPreferences: any;
  sessionMetadata: any;
}

/**
 * Chat AI Service Class
 */
export class ChatAIService {
  private static aiService = AIService.getInstance();
  private static promptService = PromptTemplateService.getInstance();
  private static formatter = ResponseFormatterService.getInstance();
  private static initialized = false;
  private static conversationContexts: Map<string, ConversationContext> = new Map();

  /**
   * Initialize the service
   */
  static async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await this.aiService.initialize();
      await this.loadPromptTemplates();
      this.initialized = true;
      Logger.info('✅ ChatAIService initialized');
    } catch (error) {
      Logger.error('❌ Failed to initialize ChatAIService:', error);
      throw error;
    }
  }

  /**
   * Send a message to AI and get response
   */
  static async sendMessage(request: {
    message: string;
    conversationId?: string;
    personality?: string;
    context?: any;
    options?: {
      style?: 'formal' | 'casual' | 'friendly';
      maxLength?: number;
      includeEmoji?: boolean;
    };
  }): Promise<ChatAIResponse> {
    await this.ensureInitialized();

    try {
      const conversationId = request.conversationId || this.generateConversationId();
      const personality = request.personality || 'friendly';
      
      // Get or create conversation context
      const context = this.getOrCreateContext(conversationId, personality, request.context);
      
      // Add user message to history
      const userMessage: AIMessage = {
        role: 'user',
        content: request.message,
        timestamp: new Date(),
      };
      context.conversationHistory.push(userMessage);

      // Build AI request
      const aiRequest = await this.buildChatRequest(context, request.options);
      
      // Send request
      const response = await this.aiService.sendRequest(aiRequest);
      
      // Add AI response to history
      const aiMessage: AIMessage = {
        role: 'assistant',
        content: response.content,
        timestamp: new Date(),
      };
      context.conversationHistory.push(aiMessage);

      // Update context
      this.conversationContexts.set(conversationId, context);

      return this.formatter.formatChatResponse({
        type: 'message',
        rawResponse: response.content,
        confidence: this.calculateConfidence(response),
        conversationId,
        context: context,
      });

    } catch (error) {
      Logger.error('Failed to send chat message:', error);
      throw error;
    }
  }

  /**
   * Send a streaming message to AI
   */
  static async* sendStreamingMessage(request: {
    message: string;
    conversationId?: string;
    personality?: string;
    context?: any;
    options?: {
      style?: 'formal' | 'casual' | 'friendly';
      maxLength?: number;
      includeEmoji?: boolean;
    };
  }): AsyncGenerator<{ delta: string; isComplete: boolean; conversationId: string }, void, unknown> {
    await this.ensureInitialized();

    try {
      const conversationId = request.conversationId || this.generateConversationId();
      const personality = request.personality || 'friendly';
      
      // Get or create conversation context
      const context = this.getOrCreateContext(conversationId, personality, request.context);
      
      // Add user message to history
      const userMessage: AIMessage = {
        role: 'user',
        content: request.message,
        timestamp: new Date(),
      };
      context.conversationHistory.push(userMessage);

      // Build AI request
      const aiRequest = await this.buildChatRequest(context, request.options);
      
      // Send streaming request
      let fullResponse = '';
      const streamGenerator = this.aiService.sendStreamingRequest(aiRequest);
      
      for await (const chunk of streamGenerator) {
        fullResponse += chunk.delta;
        
        yield {
          delta: chunk.delta,
          isComplete: chunk.isComplete,
          conversationId,
        };
        
        if (chunk.isComplete) {
          // Add AI response to history
          const aiMessage: AIMessage = {
            role: 'assistant',
            content: fullResponse,
            timestamp: new Date(),
          };
          context.conversationHistory.push(aiMessage);
          
          // Update context
          this.conversationContexts.set(conversationId, context);
          break;
        }
      }

    } catch (error) {
      Logger.error('Failed to send streaming chat message:', error);
      throw error;
    }
  }

  /**
   * Continue an existing conversation
   */
  static async continueConversation(request: {
    conversationId: string;
    message: string;
    options?: any;
  }): Promise<ChatAIResponse> {
    return this.sendMessage({
      message: request.message,
      conversationId: request.conversationId,
      options: request.options,
    });
  }

  /**
   * Analyze conversation context and sentiment
   */
  static async analyzeContext(request: {
    conversationId: string;
  }): Promise<ChatAIResponse> {
    await this.ensureInitialized();

    try {
      const context = this.conversationContexts.get(request.conversationId);
      if (!context) {
        throw new Error('Conversation not found');
      }

      const prompt = await this.promptService.getPrompt('chat_analyze_context', {
        conversationHistory: JSON.stringify(context.conversationHistory, null, 2),
        personality: context.personality,
      });

      const aiRequest: AIRequest = {
        messages: [
          { role: 'system', content: prompt.systemPrompt, timestamp: new Date() },
          { role: 'user', content: prompt.userPrompt, timestamp: new Date() },
        ],
        context: 'chat',
        metadata: { type: 'analyze_context' },
      };

      const response = await this.aiService.sendRequest(aiRequest);
      
      return this.formatter.formatChatResponse({
        type: 'analyze_context',
        rawResponse: response.content,
        confidence: this.calculateConfidence(response),
        conversationId: request.conversationId,
        context: context,
      });

    } catch (error) {
      Logger.error('Failed to analyze conversation context:', error);
      throw error;
    }
  }

  /**
   * Switch AI personality for a conversation
   */
  static async switchPersonality(request: {
    conversationId: string;
    newPersonality: string;
  }): Promise<ChatAIResponse> {
    await this.ensureInitialized();

    try {
      const context = this.conversationContexts.get(request.conversationId);
      if (!context) {
        throw new Error('Conversation not found');
      }

      // Update personality
      context.personality = request.newPersonality;
      this.conversationContexts.set(request.conversationId, context);

      return {
        type: 'switch_personality',
        result: {
          message: `Personality switched to ${request.newPersonality}`,
          personality: request.newPersonality,
        },
        confidence: 1.0,
        conversationState: {
          conversationId: request.conversationId,
          personality: request.newPersonality,
        },
      };

    } catch (error) {
      Logger.error('Failed to switch personality:', error);
      throw error;
    }
  }

  /**
   * Get conversation history
   */
  static getConversationHistory(conversationId: string): AIMessage[] {
    const context = this.conversationContexts.get(conversationId);
    return context?.conversationHistory || [];
  }

  /**
   * Clear conversation history
   */
  static clearConversation(conversationId: string): void {
    this.conversationContexts.delete(conversationId);
  }

  /**
   * Get all active conversations
   */
  static getActiveConversations(): string[] {
    return Array.from(this.conversationContexts.keys());
  }

  /**
   * Health check for the service
   */
  static async healthCheck(): Promise<boolean> {
    try {
      await this.ensureInitialized();
      const healthStatus = await this.aiService.healthCheck();
      return Object.values(healthStatus).some(status => status);
    } catch {
      return false;
    }
  }

  // Private helper methods

  private static async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  private static generateConversationId(): string {
    return `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static getOrCreateContext(
    conversationId: string, 
    personality: string, 
    userContext?: any
  ): ConversationContext {
    let context = this.conversationContexts.get(conversationId);
    
    if (!context) {
      context = {
        personality,
        conversationHistory: [],
        userPreferences: userContext?.preferences || {},
        sessionMetadata: {
          startTime: new Date(),
          messageCount: 0,
        },
      };
    }
    
    return context;
  }

  private static async buildChatRequest(
    context: ConversationContext, 
    options?: any
  ): Promise<AIRequest> {
    const personalityPrompt = await this.getPersonalityPrompt(context.personality);
    const stylePrompt = this.getStylePrompt(options?.style || 'friendly');
    
    const systemMessage: AIMessage = {
      role: 'system',
      content: `${personalityPrompt}\n\n${stylePrompt}`,
      timestamp: new Date(),
    };

    // Limit conversation history to last 20 messages to avoid token limits
    const recentHistory = context.conversationHistory.slice(-20);
    
    return {
      messages: [systemMessage, ...recentHistory],
      context: 'chat',
      maxTokens: options?.maxLength || 1000,
      temperature: 0.8, // Higher temperature for more creative responses
      metadata: {
        conversationId: context.sessionMetadata.conversationId,
        personality: context.personality,
      },
    };
  }

  private static async getPersonalityPrompt(personality: string): Promise<string> {
    const personalities = {
      friendly: 'You are a friendly, helpful, and empathetic AI assistant. You communicate warmly and show genuine interest in helping users.',
      professional: 'You are a professional, knowledgeable, and efficient AI assistant. You provide clear, accurate, and well-structured responses.',
      casual: 'You are a casual, relaxed, and approachable AI assistant. You communicate in a laid-back, conversational style.',
      enthusiastic: 'You are an enthusiastic, energetic, and positive AI assistant. You show excitement and encouragement in your responses.',
      wise: 'You are a wise, thoughtful, and insightful AI assistant. You provide deep, meaningful responses with careful consideration.',
    };

    return personalities[personality] || personalities.friendly;
  }

  private static getStylePrompt(style: string): string {
    const styles = {
      formal: 'Use formal language and professional tone. Avoid contractions and casual expressions.',
      casual: 'Use casual, conversational language. Feel free to use contractions and informal expressions.',
      friendly: 'Use warm, friendly language that makes the user feel comfortable and valued.',
    };

    return styles[style] || styles.friendly;
  }

  private static async loadPromptTemplates(): Promise<void> {
    const templates = [
      {
        id: 'chat_analyze_context',
        name: 'Conversation Context Analysis',
        context: 'chat' as const,
        systemPrompt: `You are an expert conversation analyst. Analyze the given conversation to understand:
- Overall sentiment and mood
- Main topics discussed
- User's emotional state and needs
- Conversation flow and engagement level
- Suggestions for improving the interaction`,
        userPromptTemplate: `Analyze this conversation:

Conversation History: {conversationHistory}
Current Personality: {personality}

Provide analysis including:
1. Sentiment analysis
2. Key topics
3. User engagement level
4. Emotional indicators
5. Recommendations for better interaction

Format as structured JSON.`,
        variables: ['conversationHistory', 'personality'],
      },
    ];

    for (const template of templates) {
      await this.promptService.addTemplate(template);
    }
  }

  private static calculateConfidence(response: any): number {
    const contentLength = response.content?.length || 0;
    const hasCoherentStructure = response.content?.split('.').length > 1;
    
    let confidence = 0.6; // Base confidence for chat
    
    if (contentLength > 50) confidence += 0.2;
    if (hasCoherentStructure) confidence += 0.1;
    if (response.finishReason === 'stop') confidence += 0.1;
    
    return Math.min(confidence, 1.0);
  }
}
