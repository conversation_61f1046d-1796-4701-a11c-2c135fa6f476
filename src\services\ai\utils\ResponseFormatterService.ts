/**
 * Response Formatter Service
 * 
 * خدمة تنسيق وتحليل ردود الذكاء الاصطناعي
 */

import { 
  PlannerAIResponse, 
  HealthAIResponse, 
  LearningAIResponse, 
  ChatAIResponse 
} from '../types';
import { Logger } from '@/core/error/Logger';

interface FormatterRequest {
  type: string;
  rawResponse: string;
  confidence: number;
  conversationId?: string;
  context?: any;
}

/**
 * Response Formatter Service Class
 */
export class ResponseFormatterService {
  private static instance: ResponseFormatterService;

  static getInstance(): ResponseFormatterService {
    if (!ResponseFormatterService.instance) {
      ResponseFormatterService.instance = new ResponseFormatterService();
    }
    return ResponseFormatterService.instance;
  }

  /**
   * Format Planner AI Response
   */
  formatPlannerResponse(request: FormatterRequest): PlannerAIResponse {
    try {
      const parsed = this.tryParseJSON(request.rawResponse);
      
      return {
        type: request.type as any,
        result: this.extractPlannerResult(parsed, request.type),
        confidence: request.confidence,
        reasoning: this.extractReasoning(parsed, request.rawResponse),
      };
    } catch (error) {
      Logger.error('Failed to format planner response:', error);
      return this.createFallbackPlannerResponse(request);
    }
  }

  /**
   * Format Health AI Response
   */
  formatHealthResponse(request: FormatterRequest): HealthAIResponse {
    try {
      const parsed = this.tryParseJSON(request.rawResponse);
      
      return {
        type: request.type as any,
        result: this.extractHealthResult(parsed, request.type),
        confidence: request.confidence,
        sources: this.extractSources(parsed),
      };
    } catch (error) {
      Logger.error('Failed to format health response:', error);
      return this.createFallbackHealthResponse(request);
    }
  }

  /**
   * Format Learning AI Response
   */
  formatLearningResponse(request: FormatterRequest): LearningAIResponse {
    try {
      const parsed = this.tryParseJSON(request.rawResponse);
      
      return {
        type: request.type as any,
        result: this.extractLearningResult(parsed, request.type),
        confidence: request.confidence,
        adaptations: this.extractAdaptations(parsed),
      };
    } catch (error) {
      Logger.error('Failed to format learning response:', error);
      return this.createFallbackLearningResponse(request);
    }
  }

  /**
   * Format Chat AI Response
   */
  formatChatResponse(request: FormatterRequest): ChatAIResponse {
    try {
      const parsed = this.tryParseJSON(request.rawResponse);
      
      return {
        type: request.type as any,
        result: this.extractChatResult(parsed, request.type, request.rawResponse),
        confidence: request.confidence,
        conversationState: request.context,
      };
    } catch (error) {
      Logger.error('Failed to format chat response:', error);
      return this.createFallbackChatResponse(request);
    }
  }

  /**
   * Extract structured data from AI response
   */
  extractStructuredData(response: string): any {
    try {
      // Try to find JSON in the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      // Try to extract key-value pairs
      const lines = response.split('\n');
      const data: any = {};
      
      for (const line of lines) {
        const colonIndex = line.indexOf(':');
        if (colonIndex > 0) {
          const key = line.substring(0, colonIndex).trim();
          const value = line.substring(colonIndex + 1).trim();
          data[key] = value;
        }
      }

      return Object.keys(data).length > 0 ? data : null;
    } catch {
      return null;
    }
  }

  /**
   * Extract action items from response
   */
  extractActionItems(response: string): string[] {
    const actionPatterns = [
      /(?:^|\n)\s*[-*]\s*(.+)/gm,
      /(?:^|\n)\s*\d+\.\s*(.+)/gm,
      /(?:action|todo|task|step):\s*(.+)/gim,
    ];

    const actions: string[] = [];
    
    for (const pattern of actionPatterns) {
      let match;
      while ((match = pattern.exec(response)) !== null) {
        const action = match[1].trim();
        if (action && !actions.includes(action)) {
          actions.push(action);
        }
      }
    }

    return actions;
  }

  /**
   * Extract recommendations from response
   */
  extractRecommendations(response: string): string[] {
    const recommendationPatterns = [
      /(?:recommend|suggest|advise)(?:ed|s|ing)?:?\s*(.+)/gim,
      /(?:you should|consider|try):\s*(.+)/gim,
      /(?:^|\n)\s*recommendation\s*\d*:?\s*(.+)/gim,
    ];

    const recommendations: string[] = [];
    
    for (const pattern of recommendationPatterns) {
      let match;
      while ((match = pattern.exec(response)) !== null) {
        const recommendation = match[1].trim();
        if (recommendation && !recommendations.includes(recommendation)) {
          recommendations.push(recommendation);
        }
      }
    }

    return recommendations;
  }

  /**
   * Extract insights from response
   */
  extractInsights(response: string): string[] {
    const insightPatterns = [
      /(?:insight|observation|finding):\s*(.+)/gim,
      /(?:notice|observe|see) that\s*(.+)/gim,
      /(?:analysis shows|data indicates|results suggest)\s*(.+)/gim,
    ];

    const insights: string[] = [];
    
    for (const pattern of insightPatterns) {
      let match;
      while ((match = pattern.exec(response)) !== null) {
        const insight = match[1].trim();
        if (insight && !insights.includes(insight)) {
          insights.push(insight);
        }
      }
    }

    return insights;
  }

  // Private helper methods

  private tryParseJSON(text: string): any {
    try {
      // Try to parse the entire response as JSON
      return JSON.parse(text);
    } catch {
      // Try to extract JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0]);
        } catch {
          return null;
        }
      }
      return null;
    }
  }

  private extractPlannerResult(parsed: any, type: string): any {
    if (parsed) {
      return {
        optimizedSchedule: parsed.optimizedSchedule || parsed.schedule,
        suggestedTasks: parsed.suggestedTasks || parsed.tasks,
        priorityAnalysis: parsed.priorityAnalysis || parsed.priorities,
        reminders: parsed.reminders,
        productivityScore: parsed.productivityScore || parsed.score,
        insights: parsed.insights || this.extractInsights(JSON.stringify(parsed)),
        recommendations: parsed.recommendations || this.extractRecommendations(JSON.stringify(parsed)),
      };
    }

    return {
      insights: [],
      recommendations: [],
    };
  }

  private extractHealthResult(parsed: any, type: string): any {
    if (parsed) {
      return {
        analysis: parsed.analysis || parsed.summary,
        recommendations: parsed.recommendations || this.extractRecommendations(JSON.stringify(parsed)),
        riskLevel: parsed.riskLevel || parsed.risk || 'low',
        urgentCare: parsed.urgentCare || parsed.urgent || false,
        tips: parsed.tips || parsed.advice || [],
        plan: parsed.plan || parsed.treatment,
        disclaimer: parsed.disclaimer || 'This is AI-generated advice. Consult healthcare professionals for medical decisions.',
      };
    }

    return {
      analysis: 'Unable to analyze the provided information.',
      recommendations: [],
      riskLevel: 'low',
      urgentCare: false,
      tips: [],
      disclaimer: 'This is AI-generated advice. Consult healthcare professionals for medical decisions.',
    };
  }

  private extractLearningResult(parsed: any, type: string): any {
    if (parsed) {
      return {
        learningPath: parsed.learningPath || parsed.path,
        skillAssessment: parsed.skillAssessment || parsed.assessment,
        recommendations: parsed.recommendations || this.extractRecommendations(JSON.stringify(parsed)),
        quiz: parsed.quiz || parsed.questions,
        feedback: parsed.feedback || parsed.response,
        nextSteps: parsed.nextSteps || parsed.steps || [],
        estimatedTime: parsed.estimatedTime || parsed.duration,
      };
    }

    return {
      recommendations: [],
      nextSteps: [],
    };
  }

  private extractChatResult(parsed: any, type: string, rawResponse: string): any {
    if (type === 'analyze_context' && parsed) {
      return {
        contextAnalysis: parsed.analysis || parsed.context,
        sentiment: parsed.sentiment || 'neutral',
        topics: parsed.topics || [],
        suggestions: parsed.suggestions || this.extractRecommendations(rawResponse),
      };
    }

    return {
      message: rawResponse,
      sentiment: this.detectSentiment(rawResponse),
      topics: this.extractTopics(rawResponse),
      suggestions: this.extractRecommendations(rawResponse),
    };
  }

  private extractReasoning(parsed: any, fallback: string): string {
    if (parsed?.reasoning) return parsed.reasoning;
    if (parsed?.explanation) return parsed.explanation;
    if (parsed?.rationale) return parsed.rationale;
    
    // Extract reasoning from text
    const reasoningMatch = fallback.match(/(?:because|since|due to|reasoning|explanation):\s*(.+)/i);
    return reasoningMatch ? reasoningMatch[1] : 'AI analysis based on provided data';
  }

  private extractSources(parsed: any): string[] {
    if (parsed?.sources && Array.isArray(parsed.sources)) {
      return parsed.sources;
    }
    return [];
  }

  private extractAdaptations(parsed: any): string[] {
    if (parsed?.adaptations && Array.isArray(parsed.adaptations)) {
      return parsed.adaptations;
    }
    return [];
  }

  private detectSentiment(text: string): string {
    const positiveWords = ['good', 'great', 'excellent', 'happy', 'positive', 'wonderful'];
    const negativeWords = ['bad', 'terrible', 'awful', 'sad', 'negative', 'horrible'];
    
    const lowerText = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private extractTopics(text: string): string[] {
    // Simple topic extraction based on common patterns
    const topics: string[] = [];
    const topicPatterns = [
      /(?:about|regarding|concerning)\s+([a-zA-Z\s]+)/gi,
      /(?:topic|subject):\s*([a-zA-Z\s]+)/gi,
    ];

    for (const pattern of topicPatterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const topic = match[1].trim();
        if (topic && !topics.includes(topic)) {
          topics.push(topic);
        }
      }
    }

    return topics;
  }

  // Fallback response creators

  private createFallbackPlannerResponse(request: FormatterRequest): PlannerAIResponse {
    return {
      type: request.type as any,
      result: {
        insights: this.extractInsights(request.rawResponse),
        recommendations: this.extractRecommendations(request.rawResponse),
      },
      confidence: Math.max(request.confidence - 0.3, 0.1),
      reasoning: 'Fallback parsing due to response format issues',
    };
  }

  private createFallbackHealthResponse(request: FormatterRequest): HealthAIResponse {
    return {
      type: request.type as any,
      result: {
        analysis: request.rawResponse,
        recommendations: this.extractRecommendations(request.rawResponse),
        riskLevel: 'low',
        urgentCare: false,
        tips: this.extractActionItems(request.rawResponse),
        disclaimer: 'This is AI-generated advice. Consult healthcare professionals for medical decisions.',
      },
      confidence: Math.max(request.confidence - 0.3, 0.1),
      sources: [],
    };
  }

  private createFallbackLearningResponse(request: FormatterRequest): LearningAIResponse {
    return {
      type: request.type as any,
      result: {
        recommendations: this.extractRecommendations(request.rawResponse),
        nextSteps: this.extractActionItems(request.rawResponse),
        feedback: request.rawResponse,
      },
      confidence: Math.max(request.confidence - 0.3, 0.1),
      adaptations: [],
    };
  }

  private createFallbackChatResponse(request: FormatterRequest): ChatAIResponse {
    return {
      type: request.type as any,
      result: {
        message: request.rawResponse,
        sentiment: this.detectSentiment(request.rawResponse),
        topics: this.extractTopics(request.rawResponse),
        suggestions: this.extractRecommendations(request.rawResponse),
      },
      confidence: Math.max(request.confidence - 0.2, 0.1),
      conversationState: request.context,
    };
  }
}
