/**
 * English Translations
 *
 * Complete English language translations for LifeAI Assistant
 */

const enTranslations = {
  // Common/General
  common: {
    ok: 'OK',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    done: 'Done',
    next: 'Next',
    previous: 'Previous',
    back: 'Back',
    close: 'Close',
    loading: 'Loading...',
    retry: 'Retry',
    refresh: 'Refresh',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    share: 'Share',
    copy: 'Copy',
    paste: 'Paste',
    select: 'Select',
    selectAll: 'Select All',
    clear: 'Clear',
    reset: 'Reset',
    submit: 'Submit',
    confirm: 'Confirm',
    yes: 'Yes',
    no: 'No',
    enable: 'Enable',
    disable: 'Disable',
    on: 'On',
    off: 'Off',
    online: 'Online',
    offline: 'Offline',
    connected: 'Connected',
    disconnected: 'Disconnected',
  },

  // Authentication
  auth: {
    login: 'Login',
    logout: 'Logout',
    register: 'Register',
    signIn: 'Sign In',
    signUp: 'Sign Up',
    signOut: 'Sign Out',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    firstName: 'First Name',
    lastName: 'Last Name',
    phoneNumber: 'Phone Number',
    dateOfBirth: 'Date of Birth',
    forgotPassword: 'Forgot Password?',
    resetPassword: 'Reset Password',
    rememberMe: 'Remember Me',
    createAccount: 'Create Account',
    alreadyHaveAccount: 'Already have an account?',
    dontHaveAccount: "Don't have an account?",
    termsAndConditions: 'Terms and Conditions',
    privacyPolicy: 'Privacy Policy',
    acceptTerms: 'I accept the Terms and Conditions',
    acceptPrivacy: 'I accept the Privacy Policy',
    marketingConsent: 'I agree to receive marketing communications',
    biometricAuth: 'Biometric Authentication',
    enableBiometric: 'Enable Biometric Authentication',
    useBiometric: 'Use Biometric Authentication',
    biometricPrompt: 'Authenticate to access LifeAI Assistant',
    biometricFallback: 'Use Passcode',
    loginSuccess: 'Login successful',
    logoutSuccess: 'Logout successful',
    registrationSuccess: 'Registration successful',
    passwordResetSent: 'Password reset email sent',
    invalidCredentials: 'Invalid email or password',
    emailAlreadyExists: 'Email already exists',
    weakPassword: 'Password is too weak',
    emailNotVerified: 'Please verify your email address',
    accountLocked: 'Account is temporarily locked',
    sessionExpired: 'Session expired, please login again',
  },

  // Chat/AI
  chat: {
    newChat: 'New Chat',
    chatHistory: 'Chat History',
    conversation: 'Conversation',
    message: 'Message',
    sendMessage: 'Send Message',
    typeMessage: 'Type a message...',
    voiceMessage: 'Voice Message',
    recordVoice: 'Record Voice',
    stopRecording: 'Stop Recording',
    playMessage: 'Play Message',
    pauseMessage: 'Pause Message',
    deleteMessage: 'Delete Message',
    copyMessage: 'Copy Message',
    shareMessage: 'Share Message',
    aiTyping: 'AI is typing...',
    aiThinking: 'AI is thinking...',
    regenerateResponse: 'Regenerate Response',
    clearChat: 'Clear Chat',
    exportChat: 'Export Chat',
    chatSettings: 'Chat Settings',
    conversationStyle: 'Conversation Style',
    responseLength: 'Response Length',
    formal: 'Formal',
    casual: 'Casual',
    friendly: 'Friendly',
    short: 'Short',
    medium: 'Medium',
    detailed: 'Detailed',
    proactiveAssistance: 'Proactive Assistance',
    learningFromInteractions: 'Learning from Interactions',
    contextAwareness: 'Context Awareness',
    messageNotSent: 'Message not sent',
    retryMessage: 'Retry sending message',
    connectionError: 'Connection error, please try again',
    aiUnavailable: 'AI service is temporarily unavailable',
  },
};

export default enTranslations;