/**
 * Health Dashboard Screen
 * 
 * لوحة تحكم الصحة الرئيسية مع الذكاء الاصطناعي
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';

// Shared components
import { Container, Card, Button, Loading } from '@/shared/components';
import { useTheme } from '@/ui/theme/useTheme';

// Feature components
import HealthMetricCard from '../components/HealthMetricCard';
import AIHealthAssistant from '../components/AIHealthAssistant';
import MedicationReminder from '../components/MedicationReminder';
import FitnessTracker from '../components/FitnessTracker';
import HealthChart from '../components/HealthChart';

// Hooks
import { useHealthTracking } from '../hooks/useHealthTracking';
import { useMedicationReminders } from '../hooks/useMedicationReminders';
import { useHealthInsights } from '../hooks/useHealthInsights';

// Types
import type { HealthMetric, AIHealthRecommendation } from '../../domain/entities';

/**
 * Health Dashboard Screen Component
 */
const HealthDashboardScreen: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigation = useNavigation();

  // State
  const [refreshing, setRefreshing] = useState(false);
  const [selectedMetric, setSelectedMetric] = useState<string>('overview');

  // Hooks
  const {
    healthProfile,
    todayMetrics,
    weeklyTrends,
    isLoading: healthLoading,
    refreshHealthData,
    updateHealthMetric,
  } = useHealthTracking();

  const {
    upcomingMedications,
    missedMedications,
    isLoading: medicationLoading,
    refreshMedications,
    markMedicationTaken,
  } = useMedicationReminders();

  const {
    aiRecommendations,
    healthInsights,
    riskAssessment,
    isLoading: insightsLoading,
    refreshInsights,
  } = useHealthInsights();

  // Effects
  useEffect(() => {
    refreshHealthData();
    refreshMedications();
    refreshInsights();
  }, []);

  // Handlers
  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      refreshHealthData(),
      refreshMedications(),
      refreshInsights(),
    ]);
    setRefreshing(false);
  };

  const handleSymptomChecker = () => {
    navigation.navigate('SymptomCheckerScreen');
  };

  const handleMedicationManagement = () => {
    navigation.navigate('MedicationScreen');
  };

  const handleFitnessTracking = () => {
    navigation.navigate('FitnessTrackingScreen');
  };

  const handleHealthInsights = () => {
    navigation.navigate('HealthInsightsScreen');
  };

  const handleAIRecommendation = (recommendation: AIHealthRecommendation) => {
    // Handle AI recommendation actions
    console.log('Applying health recommendation:', recommendation);
  };

  // Loading state
  if (healthLoading || medicationLoading || insightsLoading) {
    return (
      <Container style={styles.container}>
        <Loading />
      </Container>
    );
  }

  return (
    <Container style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            {t('healthAI.dashboard.title')}
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            {t('healthAI.dashboard.subtitle')}
          </Text>
        </View>

        {/* Health Score Overview */}
        <Card style={styles.scoreCard}>
          <View style={styles.scoreContainer}>
            <View style={styles.scoreCircle}>
              <Text style={[styles.scoreNumber, { color: theme.colors.primary.main }]}>
                {healthProfile?.healthScore || 0}
              </Text>
              <Text style={[styles.scoreLabel, { color: theme.colors.textSecondary }]}>
                {t('healthAI.healthScore')}
              </Text>
            </View>
            <View style={styles.scoreDetails}>
              <Text style={[styles.scoreStatus, { color: theme.colors.success.main }]}>
                {t('healthAI.status.good')}
              </Text>
              <Text style={[styles.scoreDescription, { color: theme.colors.textSecondary }]}>
                {t('healthAI.dashboard.scoreDescription')}
              </Text>
            </View>
          </View>
        </Card>

        {/* AI Health Assistant */}
        <AIHealthAssistant
          recommendations={aiRecommendations}
          onRecommendationAction={handleAIRecommendation}
        />

        {/* Quick Health Metrics */}
        <View style={styles.metricsGrid}>
          <HealthMetricCard
            title={t('healthAI.metrics.heartRate')}
            value={todayMetrics?.heartRate?.value}
            unit="bpm"
            trend={todayMetrics?.heartRate?.trend}
            color={theme.colors.error.main}
            onPress={() => setSelectedMetric('heartRate')}
          />
          <HealthMetricCard
            title={t('healthAI.metrics.bloodPressure')}
            value={`${todayMetrics?.bloodPressure?.systolic}/${todayMetrics?.bloodPressure?.diastolic}`}
            unit="mmHg"
            trend={todayMetrics?.bloodPressure?.trend}
            color={theme.colors.warning.main}
            onPress={() => setSelectedMetric('bloodPressure')}
          />
          <HealthMetricCard
            title={t('healthAI.metrics.steps')}
            value={todayMetrics?.steps?.value}
            unit="steps"
            trend={todayMetrics?.steps?.trend}
            color={theme.colors.success.main}
            onPress={() => setSelectedMetric('steps')}
          />
          <HealthMetricCard
            title={t('healthAI.metrics.sleep')}
            value={todayMetrics?.sleep?.hours}
            unit="hrs"
            trend={todayMetrics?.sleep?.trend}
            color={theme.colors.info.main}
            onPress={() => setSelectedMetric('sleep')}
          />
        </View>

        {/* Medication Reminders */}
        {upcomingMedications.length > 0 && (
          <Card style={styles.medicationCard}>
            <View style={styles.cardHeader}>
              <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
                {t('healthAI.medications.upcoming')}
              </Text>
              <TouchableOpacity onPress={handleMedicationManagement}>
                <Text style={[styles.viewAllText, { color: theme.colors.primary.main }]}>
                  {t('common.viewAll')}
                </Text>
              </TouchableOpacity>
            </View>
            <MedicationReminder
              medications={upcomingMedications.slice(0, 3)}
              onMedicationTaken={markMedicationTaken}
            />
          </Card>
        )}

        {/* Health Trends Chart */}
        <Card style={styles.chartCard}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('healthAI.trends.weekly')}
            </Text>
            <TouchableOpacity onPress={handleHealthInsights}>
              <Text style={[styles.viewAllText, { color: theme.colors.primary.main }]}>
                {t('common.viewDetails')}
              </Text>
            </TouchableOpacity>
          </View>
          <HealthChart
            data={weeklyTrends}
            selectedMetric={selectedMetric}
            onMetricSelect={setSelectedMetric}
          />
        </Card>

        {/* Fitness Summary */}
        <Card style={styles.fitnessCard}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('healthAI.fitness.todayActivity')}
            </Text>
            <TouchableOpacity onPress={handleFitnessTracking}>
              <Text style={[styles.viewAllText, { color: theme.colors.primary.main }]}>
                {t('common.viewMore')}
              </Text>
            </TouchableOpacity>
          </View>
          <FitnessTracker
            todayActivity={todayMetrics?.activity}
            goals={healthProfile?.fitnessGoals}
            compact={true}
          />
        </Card>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <Button
            title={t('healthAI.actions.symptomChecker')}
            onPress={handleSymptomChecker}
            style={styles.actionButton}
            icon="medical-bag"
          />
          <Button
            title={t('healthAI.actions.addMetric')}
            onPress={() => navigation.navigate('AddHealthMetric')}
            variant="outline"
            style={styles.actionButton}
            icon="plus"
          />
        </View>

        {/* Health Tips */}
        {healthInsights.length > 0 && (
          <Card style={styles.tipsCard}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('healthAI.insights.dailyTips')}
            </Text>
            {healthInsights.slice(0, 2).map((insight, index) => (
              <View key={index} style={styles.tipItem}>
                <Text style={[styles.tipText, { color: theme.colors.textSecondary }]}>
                  {insight.description}
                </Text>
              </View>
            ))}
          </Card>
        )}
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  scoreCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scoreCircle: {
    alignItems: 'center',
    marginRight: 20,
  },
  scoreNumber: {
    fontSize: 36,
    fontWeight: 'bold',
  },
  scoreLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  scoreDetails: {
    flex: 1,
  },
  scoreStatus: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  scoreDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 12,
  },
  medicationCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  chartCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  fitnessCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  tipsCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
  },
  quickActions: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  tipItem: {
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default HealthDashboardScreen;
