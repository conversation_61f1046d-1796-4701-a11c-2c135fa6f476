/**
 * Authentication Types
 * 
 * Type definitions for all authentication-related data structures
 */

/**
 * User interface representing the authenticated user
 */
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  preferences: UserPreferences;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
}

/**
 * User preferences and settings
 */
export interface UserPreferences {
  language: 'en' | 'ar';
  theme: 'light' | 'dark' | 'system';
  notifications: NotificationPreferences;
  privacy: PrivacySettings;
  ai: AIPreferences;
}

export interface NotificationPreferences {
  push: boolean;
  email: boolean;
  sms: boolean;
  aiReminders: boolean;
  weeklyReports: boolean;
}

export interface PrivacySettings {
  dataCollection: boolean;
  analytics: boolean;
  personalization: boolean;
  shareWithThirdParties: boolean;
}

export interface AIPreferences {
  conversationStyle: 'formal' | 'casual' | 'friendly';
  responseLength: 'short' | 'medium' | 'detailed';
  proactiveAssistance: boolean;
  learningFromInteractions: boolean;
}

/**
 * Authentication state
 */
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: AuthError | null;
  tokens: {
    accessToken: string | null;
    refreshToken: string | null;
    expiresAt: number | null;
  };
  biometric: {
    isEnabled: boolean;
    isAvailable: boolean;
    supportedTypes: BiometricType[];
  };
}

/**
 * Login credentials
 */
export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
  useBiometric?: boolean;
}

/**
 * Registration data
 */
export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  acceptTerms: boolean;
  acceptPrivacy: boolean;
  marketingConsent?: boolean;
}

/**
 * Password reset data
 */
export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * Authentication errors
 */
export interface AuthError {
  code: string;
  message: string;
  field?: string;
  details?: Record<string, any>;
}

/**
 * Biometric authentication types
 */
export type BiometricType = 
  | 'TouchID' 
  | 'FaceID' 
  | 'Fingerprint' 
  | 'Iris' 
  | 'Voice';

/**
 * Authentication method types
 */
export type AuthMethod = 
  | 'email' 
  | 'phone' 
  | 'google' 
  | 'apple' 
  | 'facebook' 
  | 'biometric';

/**
 * Token response from API
 */
export interface TokenResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

/**
 * API response wrapper
 */
export interface AuthApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: AuthError;
  message?: string;
}
