/**
 * Shared Components
 * 
 * Reusable UI components that are used across multiple features.
 * These components follow the design system and are highly customizable.
 */

// Layout Components
export { default as Container } from './layout/Container';
export { default as SafeAreaView } from './layout/SafeAreaView';
export { default as KeyboardAvoidingView } from './layout/KeyboardAvoidingView';
export { default as ScrollView } from './layout/ScrollView';
export { default as Card } from './layout/Card';
export { default as Divider } from './layout/Divider';

// Form Components
export { default as Input } from './form/Input';
export { default as Button } from './form/Button';
export { default as Checkbox } from './form/Checkbox';
export { default as RadioButton } from './form/RadioButton';
export { default as Switch } from './form/Switch';
export { default as Slider } from './form/Slider';
export { default as DatePicker } from './form/DatePicker';
export { default as Picker } from './form/Picker';

// Feedback Components
export { default as Loading } from './feedback/Loading';
export { default as Spinner } from './feedback/Spinner';
export { default as ProgressBar } from './feedback/ProgressBar';
export { default as Toast } from './feedback/Toast';
export { default as Alert } from './feedback/Alert';
export { default as Modal } from './feedback/Modal';
export { default as BottomSheet } from './feedback/BottomSheet';

// Navigation Components
export { default as Header } from './navigation/Header';
export { default as TabBar } from './navigation/TabBar';
export { default as BackButton } from './navigation/BackButton';
export { default as NavigationButton } from './navigation/NavigationButton';

// Display Components
export { default as Text } from './display/Text';
export { default as Heading } from './display/Heading';
export { default as Avatar } from './display/Avatar';
export { default as Badge } from './display/Badge';
export { default as Tag } from './display/Tag';
export { default as Icon } from './display/Icon';
export { default as Image } from './display/Image';
export { default as Video } from './display/Video';

// List Components
export { default as FlatList } from './list/FlatList';
export { default as SectionList } from './list/SectionList';
export { default as ListItem } from './list/ListItem';
export { default as EmptyState } from './list/EmptyState';
export { default as RefreshControl } from './list/RefreshControl';

// Animation Components
export { default as FadeIn } from './animation/FadeIn';
export { default as SlideIn } from './animation/SlideIn';
export { default as ScaleIn } from './animation/ScaleIn';
export { default as Animated } from './animation/Animated';

// Gesture Components
export { default as Swipeable } from './gesture/Swipeable';
export { default as PinchZoom } from './gesture/PinchZoom';
export { default as LongPress } from './gesture/LongPress';

// Accessibility Components
export { default as AccessibleView } from './accessibility/AccessibleView';
export { default as ScreenReader } from './accessibility/ScreenReader';
export { default as FocusManager } from './accessibility/FocusManager';

// Types for component props
export type {
  ContainerProps,
  ButtonProps,
  InputProps,
  TextProps,
  ModalProps,
  LoadingProps,
  HeaderProps,
  AvatarProps,
  IconProps,
} from './types';
