/**
 * Smart Planner AI Service
 * 
 * خدمة الذكاء الاصطناعي المتخصصة في إدارة الوقت والمهام
 */

import { AIService } from '../core/AIService';
import { PromptTemplateService } from '../utils/PromptTemplateService';
import { ResponseFormatterService } from '../utils/ResponseFormatterService';
import { 
  PlannerAIRequest, 
  PlannerAIResponse, 
  AIRequest, 
  AIMessage 
} from '../types';
import { Logger } from '@/core/error/Logger';

/**
 * Smart Planner AI Service Class
 */
export class PlannerAIService {
  private static aiService = AIService.getInstance();
  private static promptService = PromptTemplateService.getInstance();
  private static formatter = ResponseFormatterService.getInstance();
  private static initialized = false;

  /**
   * Initialize the service
   */
  static async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await this.aiService.initialize();
      await this.loadPromptTemplates();
      this.initialized = true;
      Logger.info('✅ PlannerAIService initialized');
    } catch (error) {
      Logger.error('❌ Failed to initialize PlannerAIService:', error);
      throw error;
    }
  }

  /**
   * Optimize schedule using AI
   */
  static async optimizeSchedule(request: {
    tasks: any[];
    currentSchedule?: any[];
    preferences?: any;
    constraints?: any;
  }): Promise<PlannerAIResponse> {
    await this.ensureInitialized();

    try {
      const prompt = await this.promptService.getPrompt('planner_optimize_schedule', {
        tasks: JSON.stringify(request.tasks, null, 2),
        currentSchedule: JSON.stringify(request.currentSchedule || [], null, 2),
        preferences: JSON.stringify(request.preferences || {}, null, 2),
        constraints: JSON.stringify(request.constraints || {}, null, 2),
      });

      const aiRequest: AIRequest = {
        messages: [
          { role: 'system', content: prompt.systemPrompt, timestamp: new Date() },
          { role: 'user', content: prompt.userPrompt, timestamp: new Date() },
        ],
        context: 'planner',
        metadata: { type: 'optimize_schedule' },
      };

      const response = await this.aiService.sendRequest(aiRequest);
      
      return this.formatter.formatPlannerResponse({
        type: 'optimize_schedule',
        rawResponse: response.content,
        confidence: this.calculateConfidence(response),
      });

    } catch (error) {
      Logger.error('Failed to optimize schedule:', error);
      throw error;
    }
  }

  /**
   * Suggest new tasks based on goals and context
   */
  static async suggestTasks(request: {
    goals: string[];
    currentTasks?: any[];
    timeframe?: string;
    preferences?: any;
  }): Promise<PlannerAIResponse> {
    await this.ensureInitialized();

    try {
      const prompt = await this.promptService.getPrompt('planner_suggest_tasks', {
        goals: request.goals.join(', '),
        currentTasks: JSON.stringify(request.currentTasks || [], null, 2),
        timeframe: request.timeframe || 'this week',
        preferences: JSON.stringify(request.preferences || {}, null, 2),
      });

      const aiRequest: AIRequest = {
        messages: [
          { role: 'system', content: prompt.systemPrompt, timestamp: new Date() },
          { role: 'user', content: prompt.userPrompt, timestamp: new Date() },
        ],
        context: 'planner',
        metadata: { type: 'suggest_tasks' },
      };

      const response = await this.aiService.sendRequest(aiRequest);
      
      return this.formatter.formatPlannerResponse({
        type: 'suggest_tasks',
        rawResponse: response.content,
        confidence: this.calculateConfidence(response),
      });

    } catch (error) {
      Logger.error('Failed to suggest tasks:', error);
      throw error;
    }
  }

  /**
   * Analyze task priorities using AI
   */
  static async analyzePriorities(request: {
    tasks: any[];
    deadlines?: any[];
    goals?: string[];
    context?: string;
  }): Promise<PlannerAIResponse> {
    await this.ensureInitialized();

    try {
      const prompt = await this.promptService.getPrompt('planner_analyze_priorities', {
        tasks: JSON.stringify(request.tasks, null, 2),
        deadlines: JSON.stringify(request.deadlines || [], null, 2),
        goals: request.goals?.join(', ') || '',
        context: request.context || '',
      });

      const aiRequest: AIRequest = {
        messages: [
          { role: 'system', content: prompt.systemPrompt, timestamp: new Date() },
          { role: 'user', content: prompt.userPrompt, timestamp: new Date() },
        ],
        context: 'planner',
        metadata: { type: 'analyze_priorities' },
      };

      const response = await this.aiService.sendRequest(aiRequest);
      
      return this.formatter.formatPlannerResponse({
        type: 'analyze_priorities',
        rawResponse: response.content,
        confidence: this.calculateConfidence(response),
      });

    } catch (error) {
      Logger.error('Failed to analyze priorities:', error);
      throw error;
    }
  }

  /**
   * Generate smart reminders
   */
  static async generateReminders(request: {
    tasks: any[];
    schedule?: any[];
    preferences?: any;
    userBehavior?: any;
  }): Promise<PlannerAIResponse> {
    await this.ensureInitialized();

    try {
      const prompt = await this.promptService.getPrompt('planner_generate_reminders', {
        tasks: JSON.stringify(request.tasks, null, 2),
        schedule: JSON.stringify(request.schedule || [], null, 2),
        preferences: JSON.stringify(request.preferences || {}, null, 2),
        userBehavior: JSON.stringify(request.userBehavior || {}, null, 2),
      });

      const aiRequest: AIRequest = {
        messages: [
          { role: 'system', content: prompt.systemPrompt, timestamp: new Date() },
          { role: 'user', content: prompt.userPrompt, timestamp: new Date() },
        ],
        context: 'planner',
        metadata: { type: 'generate_reminders' },
      };

      const response = await this.aiService.sendRequest(aiRequest);
      
      return this.formatter.formatPlannerResponse({
        type: 'generate_reminders',
        rawResponse: response.content,
        confidence: this.calculateConfidence(response),
      });

    } catch (error) {
      Logger.error('Failed to generate reminders:', error);
      throw error;
    }
  }

  /**
   * Assess productivity and provide insights
   */
  static async assessProductivity(request: {
    completedTasks: any[];
    timeSpent: any[];
    goals?: string[];
    period?: string;
  }): Promise<PlannerAIResponse> {
    await this.ensureInitialized();

    try {
      const prompt = await this.promptService.getPrompt('planner_assess_productivity', {
        completedTasks: JSON.stringify(request.completedTasks, null, 2),
        timeSpent: JSON.stringify(request.timeSpent, null, 2),
        goals: request.goals?.join(', ') || '',
        period: request.period || 'this week',
      });

      const aiRequest: AIRequest = {
        messages: [
          { role: 'system', content: prompt.systemPrompt, timestamp: new Date() },
          { role: 'user', content: prompt.userPrompt, timestamp: new Date() },
        ],
        context: 'planner',
        metadata: { type: 'assess_productivity' },
      };

      const response = await this.aiService.sendRequest(aiRequest);
      
      return this.formatter.formatPlannerResponse({
        type: 'assess_productivity',
        rawResponse: response.content,
        confidence: this.calculateConfidence(response),
      });

    } catch (error) {
      Logger.error('Failed to assess productivity:', error);
      throw error;
    }
  }

  /**
   * Health check for the service
   */
  static async healthCheck(): Promise<boolean> {
    try {
      await this.ensureInitialized();
      const healthStatus = await this.aiService.healthCheck();
      return Object.values(healthStatus).some(status => status);
    } catch {
      return false;
    }
  }

  // Private helper methods

  private static async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  private static async loadPromptTemplates(): Promise<void> {
    const templates = [
      {
        id: 'planner_optimize_schedule',
        name: 'Schedule Optimization',
        context: 'planner' as const,
        systemPrompt: `You are an expert AI assistant specialized in time management and schedule optimization. 
Your role is to analyze tasks, schedules, and constraints to create optimal time allocations.

Key principles:
- Prioritize high-impact tasks
- Consider energy levels and focus requirements
- Balance work and breaks
- Respect deadlines and constraints
- Optimize for productivity and well-being

Always provide structured, actionable recommendations with clear reasoning.`,
        userPromptTemplate: `Please optimize the following schedule:

Tasks: {tasks}
Current Schedule: {currentSchedule}
User Preferences: {preferences}
Constraints: {constraints}

Provide an optimized schedule with:
1. Recommended time slots for each task
2. Suggested breaks and buffer time
3. Priority adjustments if needed
4. Reasoning for changes
5. Productivity tips

Format your response as structured JSON.`,
        variables: ['tasks', 'currentSchedule', 'preferences', 'constraints'],
      },
      // Add more templates...
    ];

    for (const template of templates) {
      await this.promptService.addTemplate(template);
    }
  }

  private static calculateConfidence(response: any): number {
    // Simple confidence calculation based on response characteristics
    const contentLength = response.content?.length || 0;
    const hasStructuredData = response.content?.includes('{') && response.content?.includes('}');
    
    let confidence = 0.5; // Base confidence
    
    if (contentLength > 100) confidence += 0.2;
    if (hasStructuredData) confidence += 0.2;
    if (response.finishReason === 'stop') confidence += 0.1;
    
    return Math.min(confidence, 1.0);
  }
}
