module.exports = {
  presets: [
    'module:metro-react-native-babel-preset',
    '@babel/preset-typescript',
  ],
  plugins: [
    // React Native Reanimated plugin (must be last)
    'react-native-reanimated/plugin',
    
    // Module resolver for path aliases
    [
      'module-resolver',
      {
        root: ['./src'],
        alias: {
          '@': './src',
          '@/features': './src/features',
          '@/core': './src/core',
          '@/shared': './src/shared',
          '@/services': './src/services',
          '@/ui': './src/ui',
          '@/ai': './src/ai',
          '@/locales': './src/locales',
        },
        extensions: [
          '.ios.ts',
          '.android.ts',
          '.ts',
          '.ios.tsx',
          '.android.tsx',
          '.tsx',
          '.jsx',
          '.js',
          '.json',
        ],
      },
    ],
    
    // Optional chaining and nullish coalescing
    '@babel/plugin-proposal-optional-chaining',
    '@babel/plugin-proposal-nullish-coalescing-operator',
    
    // Class properties
    '@babel/plugin-proposal-class-properties',
    
    // Decorators (if needed)
    ['@babel/plugin-proposal-decorators', { legacy: true }],
    
    // Transform runtime
    [
      '@babel/plugin-transform-runtime',
      {
        helpers: true,
        regenerator: false,
      },
    ],
  ],
  env: {
    production: {
      plugins: [
        // Remove console.log in production
        'transform-remove-console',
        
        // Inline environment variables
        'transform-inline-environment-variables',
      ],
    },
  },
};
