/**
 * AI Configuration with Security
 * 
 * تكوين آمن لخدمات الذكاء الاصطناعي مع حماية API Keys
 */

import { ProviderType, ModelConfig, AIServiceConfig } from '../types';
import { SecureStorage } from '@/core/storage/SecureStorage';

/**
 * Secure API Key Management
 */
class SecureAPIKeyManager {
  private static instance: SecureAPIKeyManager;
  private keys: Map<string, string> = new Map();
  private initialized = false;

  static getInstance(): SecureAPIKeyManager {
    if (!SecureAPIKeyManager.instance) {
      SecureAPIKeyManager.instance = new SecureAPIKeyManager();
    }
    return SecureAPIKeyManager.instance;
  }

  /**
   * Initialize API keys from secure storage
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Load API keys from secure storage
      const openaiKey = await SecureStorage.getItem('ai_openai_key');
      const deepseekKey = await SecureStorage.getItem('ai_deepseek_key');
      const anthropicKey = await SecureStorage.getItem('ai_anthropic_key');

      if (openaiKey) this.keys.set('openai', openaiKey);
      if (deepseekKey) this.keys.set('deepseek', deepseekKey);
      if (anthropicKey) this.keys.set('anthropic', anthropicKey);

      this.initialized = true;
      console.log('🔐 API Keys loaded securely');
    } catch (error) {
      console.error('❌ Failed to load API keys:', error);
      throw new Error('Failed to initialize secure API key manager');
    }
  }

  /**
   * Set API key securely
   */
  async setAPIKey(provider: ProviderType, key: string): Promise<void> {
    try {
      await SecureStorage.setItem(`ai_${provider}_key`, key);
      this.keys.set(provider, key);
      console.log(`🔑 API key set for ${provider}`);
    } catch (error) {
      console.error(`❌ Failed to set API key for ${provider}:`, error);
      throw error;
    }
  }

  /**
   * Get API key securely
   */
  getAPIKey(provider: ProviderType): string | null {
    return this.keys.get(provider) || null;
  }

  /**
   * Remove API key
   */
  async removeAPIKey(provider: ProviderType): Promise<void> {
    try {
      await SecureStorage.removeItem(`ai_${provider}_key`);
      this.keys.delete(provider);
      console.log(`🗑️ API key removed for ${provider}`);
    } catch (error) {
      console.error(`❌ Failed to remove API key for ${provider}:`, error);
      throw error;
    }
  }

  /**
   * Check if API key exists
   */
  hasAPIKey(provider: ProviderType): boolean {
    return this.keys.has(provider) && !!this.keys.get(provider);
  }

  /**
   * Validate API key format
   */
  validateAPIKeyFormat(provider: ProviderType, key: string): boolean {
    const patterns = {
      openai: /^sk-[a-zA-Z0-9]{48}$/,
      deepseek: /^sk-[a-zA-Z0-9]{32,}$/,
      anthropic: /^sk-ant-[a-zA-Z0-9\-_]{95}$/,
      local: /^.+$/, // Any non-empty string for local
    };

    return patterns[provider]?.test(key) || false;
  }

  /**
   * Get masked API key for display
   */
  getMaskedAPIKey(provider: ProviderType): string {
    const key = this.getAPIKey(provider);
    if (!key) return 'Not set';
    
    if (key.length <= 8) return '****';
    return `${key.substring(0, 4)}...${key.substring(key.length - 4)}`;
  }
}

/**
 * Model Configurations
 */
export const MODEL_CONFIGS: Record<ProviderType, Record<string, ModelConfig>> = {
  openai: {
    'gpt-4-turbo': {
      name: 'gpt-4-turbo',
      maxTokens: 4096,
      temperature: 0.7,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      streaming: true,
    },
    'gpt-4': {
      name: 'gpt-4',
      maxTokens: 8192,
      temperature: 0.7,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      streaming: true,
    },
    'gpt-3.5-turbo': {
      name: 'gpt-3.5-turbo',
      maxTokens: 4096,
      temperature: 0.7,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      streaming: true,
    },
  },
  deepseek: {
    'deepseek-chat': {
      name: 'deepseek-chat',
      maxTokens: 4096,
      temperature: 0.7,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      streaming: true,
    },
    'deepseek-coder': {
      name: 'deepseek-coder',
      maxTokens: 4096,
      temperature: 0.3,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      streaming: true,
    },
  },
  anthropic: {
    'claude-3-opus': {
      name: 'claude-3-opus-20240229',
      maxTokens: 4096,
      temperature: 0.7,
      topP: 1,
      streaming: true,
    },
    'claude-3-sonnet': {
      name: 'claude-3-sonnet-20240229',
      maxTokens: 4096,
      temperature: 0.7,
      topP: 1,
      streaming: true,
    },
  },
  local: {
    'local-model': {
      name: 'local-model',
      maxTokens: 2048,
      temperature: 0.7,
      streaming: false,
    },
  },
};

/**
 * Provider Base URLs
 */
export const PROVIDER_URLS: Record<ProviderType, string> = {
  openai: 'https://api.openai.com/v1',
  deepseek: 'https://api.deepseek.com/v1',
  anthropic: 'https://api.anthropic.com/v1',
  local: 'http://localhost:8080/v1',
};

/**
 * Default AI Service Configuration
 */
export const DEFAULT_AI_CONFIG: AIServiceConfig = {
  defaultProvider: 'openai',
  fallbackProviders: ['deepseek', 'anthropic'],
  retryAttempts: 3,
  timeout: 30000, // 30 seconds
  caching: {
    enabled: true,
    ttl: 300000, // 5 minutes
    maxSize: 100, // 100 cached responses
  },
  rateLimiting: {
    enabled: true,
    requestsPerMinute: 60,
  },
  logging: {
    enabled: __DEV__,
    level: __DEV__ ? 'debug' : 'error',
  },
};

/**
 * Context-Specific Model Selection
 */
export const CONTEXT_MODEL_MAP: Record<string, Record<ProviderType, string>> = {
  planner: {
    openai: 'gpt-4-turbo',
    deepseek: 'deepseek-chat',
    anthropic: 'claude-3-sonnet',
    local: 'local-model',
  },
  health: {
    openai: 'gpt-4',
    deepseek: 'deepseek-chat',
    anthropic: 'claude-3-opus',
    local: 'local-model',
  },
  learning: {
    openai: 'gpt-4-turbo',
    deepseek: 'deepseek-chat',
    anthropic: 'claude-3-sonnet',
    local: 'local-model',
  },
  chat: {
    openai: 'gpt-3.5-turbo',
    deepseek: 'deepseek-chat',
    anthropic: 'claude-3-sonnet',
    local: 'local-model',
  },
  general: {
    openai: 'gpt-3.5-turbo',
    deepseek: 'deepseek-chat',
    anthropic: 'claude-3-sonnet',
    local: 'local-model',
  },
};

/**
 * AI Configuration Class
 */
export class AIConfig {
  private static keyManager = SecureAPIKeyManager.getInstance();
  private static config = DEFAULT_AI_CONFIG;

  /**
   * Initialize AI configuration
   */
  static async initialize(): Promise<void> {
    await this.keyManager.initialize();
  }

  /**
   * Get API key for provider
   */
  static getAPIKey(provider: ProviderType): string | null {
    return this.keyManager.getAPIKey(provider);
  }

  /**
   * Set API key for provider
   */
  static async setAPIKey(provider: ProviderType, key: string): Promise<void> {
    if (!this.keyManager.validateAPIKeyFormat(provider, key)) {
      throw new Error(`Invalid API key format for ${provider}`);
    }
    await this.keyManager.setAPIKey(provider, key);
  }

  /**
   * Check if provider is configured
   */
  static isProviderConfigured(provider: ProviderType): boolean {
    return this.keyManager.hasAPIKey(provider);
  }

  /**
   * Get available providers
   */
  static getAvailableProviders(): ProviderType[] {
    return Object.keys(PROVIDER_URLS).filter(provider => 
      this.isProviderConfigured(provider as ProviderType)
    ) as ProviderType[];
  }

  /**
   * Get model for context and provider
   */
  static getModelForContext(context: string, provider: ProviderType): string {
    return CONTEXT_MODEL_MAP[context]?.[provider] || 
           CONTEXT_MODEL_MAP.general[provider];
  }

  /**
   * Get model configuration
   */
  static getModelConfig(provider: ProviderType, model: string): ModelConfig | null {
    return MODEL_CONFIGS[provider]?.[model] || null;
  }

  /**
   * Get provider base URL
   */
  static getProviderURL(provider: ProviderType): string {
    return PROVIDER_URLS[provider];
  }

  /**
   * Get service configuration
   */
  static getServiceConfig(): AIServiceConfig {
    return { ...this.config };
  }

  /**
   * Update service configuration
   */
  static updateServiceConfig(updates: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  /**
   * Get masked API keys for display
   */
  static getMaskedAPIKeys(): Record<ProviderType, string> {
    const providers = Object.keys(PROVIDER_URLS) as ProviderType[];
    const masked: Record<string, string> = {};
    
    providers.forEach(provider => {
      masked[provider] = this.keyManager.getMaskedAPIKey(provider);
    });
    
    return masked as Record<ProviderType, string>;
  }
}
