/**
 * LifeAI Assistant - Main App Component
 * 
 * Root component that sets up the entire application with:
 * - Theme provider
 * - Navigation
 * - State management
 * - Internationalization
 * - Error boundaries
 */

import React, { useEffect } from 'react';
import { StatusBar, LogBox } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Provider as ReduxProvider } from 'react-redux';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Toast from 'react-native-toast-message';

// Core imports
import { store } from '@/core/store';
import { ThemeProvider } from '@/ui/theme/ThemeProvider';
import { NavigationContainer } from '@react-navigation/native';
import { RootNavigator } from '@/core/navigation/RootNavigator';

// Localization
import '@/locales';

// Services
import { AnalyticsService } from '@/services/analytics/AnalyticsService';
import { CrashReportingService } from '@/services/monitoring/CrashReportingService';
import { PerformanceService } from '@/services/monitoring/PerformanceService';

// Error boundary
import { ErrorBoundary } from '@/core/error/ErrorBoundary';

// Configuration
import AppConfig from '@/core/constants/config';

// Ignore specific warnings in development
if (__DEV__) {
  LogBox.ignoreLogs([
    'Non-serializable values were found in the navigation state',
    'VirtualizedLists should never be nested',
    'Setting a timer for a long period of time',
  ]);
}

/**
 * React Query client configuration
 */
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
    mutations: {
      retry: 1,
    },
  },
});

/**
 * Main App Component
 */
const App: React.FC = () => {
  useEffect(() => {
    // Initialize services
    const initializeServices = async () => {
      try {
        // Initialize analytics
        if (AppConfig.FEATURES.ANALYTICS) {
          await AnalyticsService.initialize();
        }

        // Initialize crash reporting
        if (AppConfig.FEATURES.CRASH_REPORTING) {
          await CrashReportingService.initialize();
        }

        // Initialize performance monitoring
        if (AppConfig.FEATURES.PERFORMANCE_MONITORING) {
          await PerformanceService.initialize();
        }

        // Track app start
        AnalyticsService.track(AppConfig.ANALYTICS.EVENTS.APP_OPEN, {
          timestamp: new Date().toISOString(),
          version: AppConfig.APP_VERSION,
        });
      } catch (error) {
        console.error('Failed to initialize services:', error);
        CrashReportingService.recordError(error as Error);
      }
    };

    initializeServices();
  }, []);

  return (
    <ErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <ReduxProvider store={store}>
            <QueryClientProvider client={queryClient}>
              <ThemeProvider>
                <NavigationContainer>
                  <StatusBar
                    barStyle="dark-content"
                    backgroundColor="transparent"
                    translucent
                  />
                  <RootNavigator />
                  <Toast />
                </NavigationContainer>
              </ThemeProvider>
            </QueryClientProvider>
          </ReduxProvider>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
};

export default App;
