/**
 * AI Service Types
 * 
 * تعريفات الأنواع لخدمات الذكاء الاصطناعي
 */

/**
 * Provider Types
 */
export type ProviderType = 'openai' | 'deepseek' | 'anthropic' | 'local';

/**
 * Context Types for Different AI Services
 */
export type ContextType = 'planner' | 'health' | 'learning' | 'chat' | 'general';

/**
 * AI Model Configuration
 */
export interface ModelConfig {
  name: string;
  maxTokens: number;
  temperature: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
  streaming?: boolean;
}

/**
 * AI Provider Interface
 */
export interface AIProvider {
  name: ProviderType;
  apiKey: string;
  baseURL?: string;
  models: Record<string, ModelConfig>;
  rateLimit?: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
  
  // Core methods
  sendRequest(request: AIRequest): Promise<AIResponse>;
  sendStreamingRequest(request: AIRequest): AsyncGenerator<StreamingResponse, void, unknown>;
  healthCheck(): Promise<boolean>;
  
  // Utility methods
  validateApiKey(): Promise<boolean>;
  getAvailableModels(): Promise<string[]>;
  estimateTokens(text: string): number;
}

/**
 * AI Request Structure
 */
export interface AIRequest {
  messages: AIMessage[];
  model?: string;
  maxTokens?: number;
  temperature?: number;
  stream?: boolean;
  context?: ContextType;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

/**
 * AI Message Structure
 */
export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  name?: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

/**
 * AI Response Structure
 */
export interface AIResponse {
  id: string;
  content: string;
  model: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason: 'stop' | 'length' | 'content_filter' | 'error';
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Streaming Response Structure
 */
export interface StreamingResponse {
  id: string;
  delta: string;
  isComplete: boolean;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  metadata?: Record<string, any>;
}

/**
 * AI Error Types
 */
export interface AIError extends Error {
  code: string;
  provider: ProviderType;
  statusCode?: number;
  retryable: boolean;
  details?: Record<string, any>;
}

/**
 * Prompt Template Structure
 */
export interface PromptTemplate {
  id: string;
  name: string;
  context: ContextType;
  systemPrompt: string;
  userPromptTemplate: string;
  variables: string[];
  examples?: PromptExample[];
  metadata?: Record<string, any>;
}

/**
 * Prompt Example for Few-Shot Learning
 */
export interface PromptExample {
  input: string;
  output: string;
  explanation?: string;
}

/**
 * Context-Specific Request Types
 */

// Smart Planner AI
export interface PlannerAIRequest {
  type: 'optimize_schedule' | 'suggest_tasks' | 'analyze_priorities' | 'generate_reminders' | 'assess_productivity';
  data: {
    tasks?: any[];
    schedule?: any[];
    preferences?: any;
    metrics?: any;
  };
  options?: {
    timeframe?: string;
    priority?: string;
    includeBreaks?: boolean;
  };
}

export interface PlannerAIResponse {
  type: PlannerAIRequest['type'];
  result: {
    optimizedSchedule?: any[];
    suggestedTasks?: any[];
    priorityAnalysis?: any;
    reminders?: any[];
    productivityScore?: number;
    insights?: string[];
    recommendations?: string[];
  };
  confidence: number;
  reasoning: string;
}

// Health AI
export interface HealthAIRequest {
  type: 'analyze_symptoms' | 'suggest_tips' | 'interpret_metrics' | 'medication_plan' | 'assess_risk';
  data: {
    symptoms?: string[];
    metrics?: any[];
    medications?: any[];
    profile?: any;
    history?: any[];
  };
  options?: {
    severity?: 'low' | 'medium' | 'high';
    urgency?: boolean;
    includeDisclaimer?: boolean;
  };
}

export interface HealthAIResponse {
  type: HealthAIRequest['type'];
  result: {
    analysis?: string;
    recommendations?: string[];
    riskLevel?: 'low' | 'medium' | 'high';
    urgentCare?: boolean;
    tips?: string[];
    plan?: any;
    disclaimer?: string;
  };
  confidence: number;
  sources?: string[];
}

// Learning AI
export interface LearningAIRequest {
  type: 'create_path' | 'assess_skills' | 'recommend_content' | 'generate_quiz' | 'provide_feedback';
  data: {
    skills?: string[];
    goals?: string[];
    currentLevel?: string;
    preferences?: any;
    progress?: any;
    answers?: any[];
  };
  options?: {
    difficulty?: 'beginner' | 'intermediate' | 'advanced';
    duration?: string;
    format?: string;
  };
}

export interface LearningAIResponse {
  type: LearningAIRequest['type'];
  result: {
    learningPath?: any[];
    skillAssessment?: any;
    recommendations?: any[];
    quiz?: any;
    feedback?: string;
    nextSteps?: string[];
    estimatedTime?: string;
  };
  confidence: number;
  adaptations?: string[];
}

// Chat AI
export interface ChatAIRequest {
  type: 'message' | 'streaming_message' | 'continue_conversation' | 'analyze_context' | 'switch_personality';
  data: {
    message?: string;
    conversation?: AIMessage[];
    personality?: string;
    context?: any;
    preferences?: any;
  };
  options?: {
    stream?: boolean;
    maxLength?: number;
    style?: 'formal' | 'casual' | 'friendly';
    includeEmoji?: boolean;
  };
}

export interface ChatAIResponse {
  type: ChatAIRequest['type'];
  result: {
    message?: string;
    contextAnalysis?: any;
    sentiment?: string;
    topics?: string[];
    suggestions?: string[];
    personality?: string;
  };
  confidence: number;
  conversationState?: any;
}

/**
 * Service Configuration
 */
export interface AIServiceConfig {
  defaultProvider: ProviderType;
  fallbackProviders: ProviderType[];
  retryAttempts: number;
  timeout: number;
  caching: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
  };
  rateLimiting: {
    enabled: boolean;
    requestsPerMinute: number;
  };
  logging: {
    enabled: boolean;
    level: 'debug' | 'info' | 'warn' | 'error';
  };
}

/**
 * Provider Status
 */
export interface ProviderStatus {
  name: ProviderType;
  isHealthy: boolean;
  latency: number;
  lastChecked: Date;
  errorCount: number;
  rateLimit: {
    remaining: number;
    resetTime: Date;
  };
}
