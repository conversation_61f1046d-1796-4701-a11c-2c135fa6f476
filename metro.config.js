const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const path = require('path');

/**
 * Metro configuration for LifeAI Assistant
 * 
 * Optimized for:
 * - TypeScript support
 * - Path aliases
 * - Asset handling
 * - Performance optimization
 */

const defaultConfig = getDefaultConfig(__dirname);

const config = {
  transformer: {
    // Enable TypeScript
    babelTransformerPath: require.resolve('metro-react-native-babel-transformer'),
    
    // Asset plugins
    assetPlugins: ['expo-asset/tools/hashAssetFiles'],
    
    // SVG support
    svgAssetPlugin: {
      hermesParser: true,
      throwIfNamespace: false,
    },
  },
  
  resolver: {
    // File extensions
    sourceExts: [
      'js',
      'jsx',
      'ts',
      'tsx',
      'json',
      'svg',
      'png',
      'jpg',
      'jpeg',
      'gif',
      'webp',
      'bmp',
      'psd',
      'tiff',
      'webm',
      'mp4',
      'mov',
      'avi',
      'wmv',
      'flv',
      'mkv',
      'mp3',
      'wav',
      'aac',
      'ogg',
      'wma',
      'flac',
    ],
    
    // Asset extensions
    assetExts: [
      'png',
      'jpg',
      'jpeg',
      'gif',
      'webp',
      'bmp',
      'psd',
      'svg',
      'tiff',
      'webm',
      'mp4',
      'mov',
      'avi',
      'wmv',
      'flv',
      'mkv',
      'mp3',
      'wav',
      'aac',
      'ogg',
      'wma',
      'flac',
      'ttf',
      'otf',
      'woff',
      'woff2',
      'eot',
    ],
    
    // Path aliases
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@/features': path.resolve(__dirname, 'src/features'),
      '@/core': path.resolve(__dirname, 'src/core'),
      '@/shared': path.resolve(__dirname, 'src/shared'),
      '@/services': path.resolve(__dirname, 'src/services'),
      '@/ui': path.resolve(__dirname, 'src/ui'),
      '@/ai': path.resolve(__dirname, 'src/ai'),
      '@/locales': path.resolve(__dirname, 'src/locales'),
    },
    
    // Platform-specific extensions
    platforms: ['ios', 'android', 'native', 'web'],
  },
  
  // Watchman configuration
  watchFolders: [
    path.resolve(__dirname, 'src'),
    path.resolve(__dirname, 'node_modules'),
  ],
  
  // Cache configuration
  cacheStores: [
    {
      name: 'FileStore',
      params: {
        root: path.join(__dirname, 'node_modules/.cache/metro'),
      },
    },
  ],
  
  // Performance optimizations
  maxWorkers: 4,
  
  // Reset cache on start (useful during development)
  resetCache: false,
};

module.exports = mergeConfig(defaultConfig, config);
