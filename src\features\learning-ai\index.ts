/**
 * Learning AI Feature Module
 * 
 * نظام تعلم ذاتي مدعوم بالذكاء الاصطناعي
 * - مسارات تعلم مخصصة
 * - تقييم المهارات
 * - محتوى تعليمي تفاعلي
 * - تتبع التقدم
 * - توصيات ذكية للتعلم
 */

// Domain Layer Exports
export * from './domain/entities';
export * from './domain/usecases';
export type { 
  LearningRepository, 
  CourseRepository, 
  SkillRepository,
  ProgressRepository 
} from './domain/repositories';

// Data Layer Exports
export { LearningRepositoryImpl } from './data/repositories/LearningRepositoryImpl';
export { CourseRepositoryImpl } from './data/repositories/CourseRepositoryImpl';
export { SkillRepositoryImpl } from './data/repositories/SkillRepositoryImpl';

// Presentation Layer Exports
export { default as LearningDashboardScreen } from './presentation/screens/LearningDashboardScreen';
export { default as CourseLibraryScreen } from './presentation/screens/CourseLibraryScreen';
export { default as SkillAssessmentScreen } from './presentation/screens/SkillAssessmentScreen';
export { default as LearningPathScreen } from './presentation/screens/LearningPathScreen';
export { default as StudySessionScreen } from './presentation/screens/StudySessionScreen';

// Components
export { default as CourseCard } from './presentation/components/CourseCard';
export { default as SkillProgressChart } from './presentation/components/SkillProgressChart';
export { default as LearningPathVisualizer } from './presentation/components/LearningPathVisualizer';
export { default as StudyTimer } from './presentation/components/StudyTimer';
export { default as QuizComponent } from './presentation/components/QuizComponent';
export { default as AITutor } from './presentation/components/AITutor';

// Hooks
export { useLearningProgress } from './presentation/hooks/useLearningProgress';
export { useSkillAssessment } from './presentation/hooks/useSkillAssessment';
export { useCourseRecommendations } from './presentation/hooks/useCourseRecommendations';
export { useStudySession } from './presentation/hooks/useStudySession';
export { useLearningAnalytics } from './presentation/hooks/useLearningAnalytics';

// Store
export { learningAISlice, learningAIActions } from './presentation/store/learningAISlice';
export type { LearningAIState } from './presentation/store/learningAISlice';

// Infrastructure
export { AITutorService } from './infrastructure/services/AITutorService';
export { LearningAnalyticsService } from './infrastructure/services/LearningAnalyticsService';
export { ContentRecommendationService } from './infrastructure/services/ContentRecommendationService';

// Types
export type {
  Course,
  Lesson,
  Skill,
  LearningPath,
  StudySession,
  Quiz,
  LearningGoal,
  ProgressMetrics,
  SkillAssessment,
  AITutorResponse,
  LearningRecommendation,
} from './domain/entities';

// Constants
export { LEARNING_AI_ROUTES } from './infrastructure/config/routes';
export { SKILL_CATEGORIES, DIFFICULTY_LEVELS } from './infrastructure/config/constants';

/**
 * Feature Module Configuration
 */
export const LearningAIModule = {
  name: 'LearningAI',
  version: '1.0.0',
  description: 'AI-powered personalized learning system',
  dependencies: ['ai', 'analytics', 'notifications', 'camera'],
  permissions: ['camera', 'microphone'],
  routes: [
    'LearningDashboardScreen',
    'CourseLibraryScreen',
    'SkillAssessmentScreen',
    'LearningPathScreen',
    'StudySessionScreen',
  ],
} as const;
