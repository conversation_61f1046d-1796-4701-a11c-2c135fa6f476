/**
 * Smart Planner Main Screen
 * 
 * الشاشة الرئيسية لميزة إدارة الوقت والمهام الذكية
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';

// Shared components
import { Container, Card, Button, Loading } from '@/shared/components';
import { useTheme } from '@/ui/theme/useTheme';

// Feature components
import TaskCard from '../components/TaskCard';
import ScheduleCalendar from '../components/ScheduleCalendar';
import ProductivityChart from '../components/ProductivityChart';
import SmartSuggestions from '../components/SmartSuggestions';

// Hooks
import { useTaskManager } from '../hooks/useTaskManager';
import { useProductivityAnalytics } from '../hooks/useProductivityAnalytics';

// Types
import type { Task, SmartSuggestion } from '../../domain/entities';

/**
 * Smart Planner Screen Component
 */
const SmartPlannerScreen: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigation = useNavigation();

  // State
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Hooks
  const {
    todayTasks,
    upcomingTasks,
    completedTasks,
    isLoading: tasksLoading,
    refreshTasks,
    completeTask,
  } = useTaskManager();

  const {
    todayMetrics,
    weeklyMetrics,
    suggestions,
    isLoading: analyticsLoading,
    refreshAnalytics,
  } = useProductivityAnalytics();

  // Effects
  useEffect(() => {
    refreshTasks();
    refreshAnalytics();
  }, []);

  // Handlers
  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([refreshTasks(), refreshAnalytics()]);
    setRefreshing(false);
  };

  const handleTaskPress = (task: Task) => {
    navigation.navigate('TaskDetails', { taskId: task.id });
  };

  const handleAddTask = () => {
    navigation.navigate('CreateTask');
  };

  const handleViewSchedule = () => {
    navigation.navigate('ScheduleScreen');
  };

  const handleViewAnalytics = () => {
    navigation.navigate('AnalyticsScreen');
  };

  const handleSuggestionAction = (suggestion: SmartSuggestion) => {
    // Handle AI suggestion actions
    console.log('Applying suggestion:', suggestion);
  };

  // Loading state
  if (tasksLoading || analyticsLoading) {
    return (
      <Container style={styles.container}>
        <Loading />
      </Container>
    );
  }

  return (
    <Container style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            {t('smartPlanner.title')}
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            {t('smartPlanner.subtitle')}
          </Text>
        </View>

        {/* Quick Stats */}
        <Card style={styles.statsCard}>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.primary.main }]}>
                {todayTasks.length}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                {t('smartPlanner.todayTasks')}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.success.main }]}>
                {completedTasks.length}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                {t('smartPlanner.completed')}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.warning.main }]}>
                {todayMetrics?.productivityScore || 0}%
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                {t('smartPlanner.productivity')}
              </Text>
            </View>
          </View>
        </Card>

        {/* Smart Suggestions */}
        {suggestions.length > 0 && (
          <SmartSuggestions
            suggestions={suggestions}
            onSuggestionAction={handleSuggestionAction}
          />
        )}

        {/* Today's Schedule Preview */}
        <Card style={styles.scheduleCard}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('smartPlanner.todaySchedule')}
            </Text>
            <TouchableOpacity onPress={handleViewSchedule}>
              <Text style={[styles.viewAllText, { color: theme.colors.primary.main }]}>
                {t('common.viewAll')}
              </Text>
            </TouchableOpacity>
          </View>
          <ScheduleCalendar
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
            compact={true}
          />
        </Card>

        {/* Today's Tasks */}
        <Card style={styles.tasksCard}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('smartPlanner.todayTasks')}
            </Text>
            <TouchableOpacity onPress={handleAddTask}>
              <Text style={[styles.addText, { color: theme.colors.primary.main }]}>
                {t('smartPlanner.addTask')}
              </Text>
            </TouchableOpacity>
          </View>
          {todayTasks.length > 0 ? (
            todayTasks.slice(0, 3).map((task) => (
              <TaskCard
                key={task.id}
                task={task}
                onPress={() => handleTaskPress(task)}
                onComplete={() => completeTask(task.id)}
                compact={true}
              />
            ))
          ) : (
            <View style={styles.emptyState}>
              <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                {t('smartPlanner.noTasksToday')}
              </Text>
            </View>
          )}
        </Card>

        {/* Productivity Chart */}
        <Card style={styles.chartCard}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('smartPlanner.weeklyProgress')}
            </Text>
            <TouchableOpacity onPress={handleViewAnalytics}>
              <Text style={[styles.viewAllText, { color: theme.colors.primary.main }]}>
                {t('common.viewDetails')}
              </Text>
            </TouchableOpacity>
          </View>
          <ProductivityChart
            data={weeklyMetrics}
            compact={true}
          />
        </Card>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <Button
            title={t('smartPlanner.addTask')}
            onPress={handleAddTask}
            style={styles.actionButton}
          />
          <Button
            title={t('smartPlanner.viewSchedule')}
            onPress={handleViewSchedule}
            variant="outline"
            style={styles.actionButton}
          />
        </View>
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  statsCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  scheduleCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  tasksCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  chartCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
  },
  addText: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  quickActions: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default SmartPlannerScreen;
