/**
 * Smart Shopping Dashboard Screen
 * 
 * لوحة تحكم التسوق الذكي مع الذكاء الاصطناعي
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';

// Shared components
import { Container, Card, Button, Loading } from '@/shared/components';
import { useTheme } from '@/ui/theme/useTheme';

// Feature components
import ShoppingListCard from '../components/ShoppingListCard';
import ProductCard from '../components/ProductCard';
import BudgetOverview from '../components/BudgetOverview';
import SmartRecommendations from '../components/SmartRecommendations';
import BarcodeScanner from '../components/BarcodeScanner';

// Hooks
import { useShoppingLists } from '../hooks/useShoppingLists';
import { useBudgetTracking } from '../hooks/useBudgetTracking';
import { useShoppingRecommendations } from '../hooks/useShoppingRecommendations';
import { usePriceTracking } from '../hooks/usePriceTracking';

// Types
import type { ShoppingList, ShoppingRecommendation, Deal } from '../../domain/entities';

/**
 * Smart Shopping Dashboard Screen Component
 */
const ShoppingDashboardScreen: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigation = useNavigation();

  // State
  const [refreshing, setRefreshing] = useState(false);
  const [showScanner, setShowScanner] = useState(false);

  // Hooks
  const {
    activeShoppingLists,
    recentPurchases,
    totalItemsCount,
    isLoading: listsLoading,
    refreshShoppingLists,
    createShoppingList,
  } = useShoppingLists();

  const {
    monthlyBudget,
    currentSpending,
    budgetCategories,
    savingsGoal,
    isLoading: budgetLoading,
    refreshBudget,
  } = useBudgetTracking();

  const {
    personalizedRecommendations,
    trendingProducts,
    seasonalDeals,
    isLoading: recommendationsLoading,
    refreshRecommendations,
  } = useShoppingRecommendations();

  const {
    priceAlerts,
    watchedProducts,
    bestDeals,
    isLoading: priceLoading,
    refreshPriceData,
  } = usePriceTracking();

  // Effects
  useEffect(() => {
    refreshShoppingLists();
    refreshBudget();
    refreshRecommendations();
    refreshPriceData();
  }, []);

  // Handlers
  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      refreshShoppingLists(),
      refreshBudget(),
      refreshRecommendations(),
      refreshPriceData(),
    ]);
    setRefreshing(false);
  };

  const handleShoppingListPress = (list: ShoppingList) => {
    navigation.navigate('ShoppingListDetails', { listId: list.id });
  };

  const handleCreateShoppingList = () => {
    navigation.navigate('CreateShoppingList');
  };

  const handleProductSearch = () => {
    navigation.navigate('ProductSearchScreen');
  };

  const handleBudgetTracking = () => {
    navigation.navigate('BudgetTrackingScreen');
  };

  const handleDealsAndOffers = () => {
    navigation.navigate('DealsAndOffersScreen');
  };

  const handleRecommendationAction = (recommendation: ShoppingRecommendation) => {
    // Handle AI shopping recommendation actions
    console.log('Applying shopping recommendation:', recommendation);
  };

  const handleBarcodeScanned = (barcode: string) => {
    setShowScanner(false);
    navigation.navigate('ProductDetails', { barcode });
  };

  // Loading state
  if (listsLoading || budgetLoading || recommendationsLoading || priceLoading) {
    return (
      <Container style={styles.container}>
        <Loading />
      </Container>
    );
  }

  return (
    <Container style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            {t('smartShopping.dashboard.title')}
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            {t('smartShopping.dashboard.subtitle')}
          </Text>
        </View>

        {/* Quick Stats */}
        <Card style={styles.statsCard}>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.primary.main }]}>
                {activeShoppingLists.length}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                {t('smartShopping.stats.activeLists')}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.warning.main }]}>
                {totalItemsCount}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                {t('smartShopping.stats.totalItems')}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.success.main }]}>
                ${currentSpending.toFixed(0)}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                {t('smartShopping.stats.monthlySpent')}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.info.main }]}>
                {priceAlerts.length}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                {t('smartShopping.stats.priceAlerts')}
              </Text>
            </View>
          </View>
        </Card>

        {/* Budget Overview */}
        <BudgetOverview
          monthlyBudget={monthlyBudget}
          currentSpending={currentSpending}
          budgetCategories={budgetCategories}
          savingsGoal={savingsGoal}
          onViewDetails={handleBudgetTracking}
        />

        {/* Smart Recommendations */}
        <SmartRecommendations
          recommendations={personalizedRecommendations}
          onRecommendationAction={handleRecommendationAction}
        />

        {/* Active Shopping Lists */}
        <Card style={styles.listsCard}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('smartShopping.lists.active')}
            </Text>
            <TouchableOpacity onPress={handleCreateShoppingList}>
              <Text style={[styles.addText, { color: theme.colors.primary.main }]}>
                {t('smartShopping.lists.create')}
              </Text>
            </TouchableOpacity>
          </View>
          {activeShoppingLists.length > 0 ? (
            activeShoppingLists.slice(0, 3).map((list) => (
              <ShoppingListCard
                key={list.id}
                shoppingList={list}
                onPress={() => handleShoppingListPress(list)}
                compact={true}
              />
            ))
          ) : (
            <View style={styles.emptyState}>
              <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                {t('smartShopping.lists.noActive')}
              </Text>
              <Button
                title={t('smartShopping.lists.createFirst')}
                onPress={handleCreateShoppingList}
                variant="outline"
                style={styles.createButton}
              />
            </View>
          )}
        </Card>

        {/* Best Deals */}
        {bestDeals.length > 0 && (
          <Card style={styles.dealsCard}>
            <View style={styles.cardHeader}>
              <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
                {t('smartShopping.deals.best')}
              </Text>
              <TouchableOpacity onPress={handleDealsAndOffers}>
                <Text style={[styles.viewAllText, { color: theme.colors.primary.main }]}>
                  {t('common.viewAll')}
                </Text>
              </TouchableOpacity>
            </View>
            {bestDeals.slice(0, 3).map((deal) => (
              <ProductCard
                key={deal.id}
                product={deal.product}
                deal={deal}
                onPress={() => navigation.navigate('ProductDetails', { productId: deal.product.id })}
                compact={true}
              />
            ))}
          </Card>
        )}

        {/* Trending Products */}
        {trendingProducts.length > 0 && (
          <Card style={styles.trendingCard}>
            <View style={styles.cardHeader}>
              <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
                {t('smartShopping.trending.title')}
              </Text>
            </View>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {trendingProducts.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  onPress={() => navigation.navigate('ProductDetails', { productId: product.id })}
                  horizontal={true}
                  compact={true}
                />
              ))}
            </ScrollView>
          </Card>
        )}

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <Button
            title={t('smartShopping.actions.scanBarcode')}
            onPress={() => setShowScanner(true)}
            style={styles.actionButton}
            icon="barcode-scan"
          />
          <Button
            title={t('smartShopping.actions.searchProducts')}
            onPress={handleProductSearch}
            variant="outline"
            style={styles.actionButton}
            icon="magnify"
          />
        </View>

        {/* Recent Purchases */}
        {recentPurchases.length > 0 && (
          <Card style={styles.recentCard}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('smartShopping.recent.purchases')}
            </Text>
            {recentPurchases.slice(0, 3).map((purchase, index) => (
              <View key={index} style={styles.purchaseItem}>
                <Text style={[styles.purchaseTitle, { color: theme.colors.text }]}>
                  {purchase.productName}
                </Text>
                <View style={styles.purchaseDetails}>
                  <Text style={[styles.purchasePrice, { color: theme.colors.success.main }]}>
                    ${purchase.price}
                  </Text>
                  <Text style={[styles.purchaseDate, { color: theme.colors.textSecondary }]}>
                    {purchase.date.toLocaleDateString()}
                  </Text>
                </View>
              </View>
            ))}
          </Card>
        )}

        {/* Price Alerts */}
        {priceAlerts.length > 0 && (
          <Card style={styles.alertsCard}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('smartShopping.alerts.price')}
            </Text>
            {priceAlerts.slice(0, 2).map((alert, index) => (
              <View key={index} style={styles.alertItem}>
                <Text style={[styles.alertText, { color: theme.colors.warning.main }]}>
                  {alert.message}
                </Text>
              </View>
            ))}
          </Card>
        )}
      </ScrollView>

      {/* Barcode Scanner Modal */}
      {showScanner && (
        <BarcodeScanner
          onBarcodeScanned={handleBarcodeScanned}
          onClose={() => setShowScanner(false)}
        />
      )}
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  statsCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  listsCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  dealsCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  trendingCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  recentCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  alertsCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
  },
  addText: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 12,
  },
  createButton: {
    minWidth: 150,
  },
  quickActions: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  purchaseItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  purchaseTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  purchaseDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  purchasePrice: {
    fontSize: 14,
    fontWeight: '600',
  },
  purchaseDate: {
    fontSize: 12,
  },
  alertItem: {
    paddingVertical: 8,
  },
  alertText: {
    fontSize: 14,
  },
});

export default ShoppingDashboardScreen;
