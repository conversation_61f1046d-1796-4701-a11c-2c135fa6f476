/**
 * Services Module - External Service Integrations
 * 
 * This module handles all external service integrations and third-party APIs:
 * - AI service providers (OpenAI, Anthropic, etc.)
 * - Analytics services (Firebase, Mixpanel)
 * - Push notification services
 * - Cloud storage services
 * - Payment processing
 * - Social media integrations
 */

// AI Services
export * from './ai';

// Analytics Services
export { AnalyticsService } from './analytics/AnalyticsService';
export { FirebaseAnalytics } from './analytics/FirebaseAnalytics';
export { MixpanelAnalytics } from './analytics/MixpanelAnalytics';

// Notification Services
export { NotificationService } from './notifications/NotificationService';
export { PushNotificationService } from './notifications/PushNotificationService';
export { LocalNotificationService } from './notifications/LocalNotificationService';

// Cloud Services
export { CloudStorageService } from './cloud/CloudStorageService';
export { BackupService } from './cloud/BackupService';
export { SyncService } from './cloud/SyncService';

// Payment Services
export { PaymentService } from './payment/PaymentService';
export { SubscriptionService } from './payment/SubscriptionService';

// Social Services
export { SocialAuthService } from './social/SocialAuthService';
export { ShareService } from './social/ShareService';

// Monitoring Services
export { CrashReportingService } from './monitoring/CrashReportingService';
export { PerformanceService } from './monitoring/PerformanceService';
export { LoggingService } from './monitoring/LoggingService';

// Types
export type {
  AIProvider,
  AnalyticsEvent,
  NotificationPayload,
  CloudFile,
  PaymentMethod,
  SocialProvider,
  CrashReport,
  PerformanceMetric,
} from './types';
