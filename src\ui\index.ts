/**
 * UI System - Design System and Theme
 * 
 * This module contains the complete design system for the LifeAI Assistant app:
 * - Theme configuration (colors, typography, spacing)
 * - Design tokens
 * - Global styles
 * - Theme provider and hooks
 */

// Theme system
export { default as theme } from './theme';
export { ThemeProvider } from './theme/ThemeProvider';
export { useTheme } from './theme/useTheme';
export { useDarkMode } from './theme/useDarkMode';

// Design tokens
export * from './tokens';

// Global styles
export { default as globalStyles } from './styles/global';
export { default as layoutStyles } from './styles/layout';
export { default as typographyStyles } from './styles/typography';

// Theme types
export type {
  Theme,
  ColorPalette,
  Typography,
  Spacing,
  BorderRadius,
  Shadows,
  ThemeMode,
} from './types';
