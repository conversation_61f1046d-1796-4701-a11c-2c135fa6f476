/**
 * Feature Registry - Central Feature Management
 * 
 * مسجل مركزي لإدارة جميع ميزات التطبيق
 * يتبع مبدأ Clean Architecture و Separation of Concerns
 */

import { FEATURES, FEATURE_CATEGORIES, FEATURE_CATEGORY_MAP } from './index';

// Feature Module Imports
import { SmartPlannerModule } from './smart-planner';
import { HealthAIModule } from './health-ai';
import { LearningAIModule } from './learning-ai';
import { SmartShoppingModule } from './smart-shopping';
import { ChatAIModule } from './chat-ai';
import { PrivacyLocalAIModule } from './privacy-local-ai';
import { CameraARModule } from './camera-ar';

/**
 * Feature Module Interface
 */
export interface FeatureModule {
  name: string;
  version: string;
  description: string;
  dependencies: string[];
  permissions?: string[];
  routes: string[];
  enabled?: boolean;
  beta?: boolean;
}

/**
 * Feature Configuration Interface
 */
export interface FeatureConfig {
  id: string;
  module: FeatureModule;
  category: string;
  icon: string;
  color: string;
  order: number;
  requiresAuth: boolean;
  requiresPremium?: boolean;
  minAppVersion?: string;
}

/**
 * Complete Feature Registry
 */
export const FEATURE_REGISTRY: Record<string, FeatureConfig> = {
  [FEATURES.SMART_PLANNER]: {
    id: FEATURES.SMART_PLANNER,
    module: SmartPlannerModule,
    category: FEATURE_CATEGORIES.PRODUCTIVITY,
    icon: 'calendar-clock',
    color: '#0ea5e9',
    order: 1,
    requiresAuth: true,
  },
  
  [FEATURES.HEALTH_AI]: {
    id: FEATURES.HEALTH_AI,
    module: HealthAIModule,
    category: FEATURE_CATEGORIES.HEALTH,
    icon: 'heart-pulse',
    color: '#ef4444',
    order: 2,
    requiresAuth: true,
  },
  
  [FEATURES.LEARNING_AI]: {
    id: FEATURES.LEARNING_AI,
    module: LearningAIModule,
    category: FEATURE_CATEGORIES.LEARNING,
    icon: 'brain',
    color: '#8b5cf6',
    order: 3,
    requiresAuth: true,
  },
  
  [FEATURES.SMART_SHOPPING]: {
    id: FEATURES.SMART_SHOPPING,
    module: SmartShoppingModule,
    category: FEATURE_CATEGORIES.SHOPPING,
    icon: 'shopping-cart',
    color: '#f59e0b',
    order: 4,
    requiresAuth: true,
  },
  
  [FEATURES.CHAT_AI]: {
    id: FEATURES.CHAT_AI,
    module: ChatAIModule,
    category: FEATURE_CATEGORIES.COMMUNICATION,
    icon: 'message-circle',
    color: '#10b981',
    order: 5,
    requiresAuth: true,
  },
  
  [FEATURES.PRIVACY_LOCAL_AI]: {
    id: FEATURES.PRIVACY_LOCAL_AI,
    module: PrivacyLocalAIModule,
    category: FEATURE_CATEGORIES.PRIVACY,
    icon: 'shield-check',
    color: '#6366f1',
    order: 6,
    requiresAuth: true,
    requiresPremium: true,
  },
  
  [FEATURES.CAMERA_AR]: {
    id: FEATURES.CAMERA_AR,
    module: CameraARModule,
    category: FEATURE_CATEGORIES.CAMERA,
    icon: 'camera',
    color: '#ec4899',
    order: 7,
    requiresAuth: true,
    beta: true,
  },
};

/**
 * Feature Registry Utilities
 */
export class FeatureRegistryManager {
  /**
   * Get all features
   */
  static getAllFeatures(): FeatureConfig[] {
    return Object.values(FEATURE_REGISTRY).sort((a, b) => a.order - b.order);
  }
  
  /**
   * Get features by category
   */
  static getFeaturesByCategory(category: string): FeatureConfig[] {
    return this.getAllFeatures().filter(feature => feature.category === category);
  }
  
  /**
   * Get feature by ID
   */
  static getFeatureById(id: string): FeatureConfig | undefined {
    return FEATURE_REGISTRY[id];
  }
  
  /**
   * Check if feature is enabled
   */
  static isFeatureEnabled(id: string): boolean {
    const feature = this.getFeatureById(id);
    return feature?.module.enabled !== false;
  }
  
  /**
   * Check if feature requires authentication
   */
  static requiresAuth(id: string): boolean {
    const feature = this.getFeatureById(id);
    return feature?.requiresAuth || false;
  }
  
  /**
   * Check if feature requires premium
   */
  static requiresPremium(id: string): boolean {
    const feature = this.getFeatureById(id);
    return feature?.requiresPremium || false;
  }
  
  /**
   * Check if feature is in beta
   */
  static isBeta(id: string): boolean {
    const feature = this.getFeatureById(id);
    return feature?.beta || false;
  }
  
  /**
   * Get feature dependencies
   */
  static getFeatureDependencies(id: string): string[] {
    const feature = this.getFeatureById(id);
    return feature?.module.dependencies || [];
  }
  
  /**
   * Get feature permissions
   */
  static getFeaturePermissions(id: string): string[] {
    const feature = this.getFeatureById(id);
    return feature?.module.permissions || [];
  }
  
  /**
   * Get feature routes
   */
  static getFeatureRoutes(id: string): string[] {
    const feature = this.getFeatureById(id);
    return feature?.module.routes || [];
  }
  
  /**
   * Get all required permissions
   */
  static getAllRequiredPermissions(): string[] {
    const permissions = new Set<string>();
    this.getAllFeatures().forEach(feature => {
      feature.module.permissions?.forEach(permission => {
        permissions.add(permission);
      });
    });
    return Array.from(permissions);
  }
  
  /**
   * Validate feature dependencies
   */
  static validateDependencies(id: string, availableServices: string[]): boolean {
    const dependencies = this.getFeatureDependencies(id);
    return dependencies.every(dep => availableServices.includes(dep));
  }
  
  /**
   * Get features grouped by category
   */
  static getFeaturesByCategories(): Record<string, FeatureConfig[]> {
    const grouped: Record<string, FeatureConfig[]> = {};
    
    Object.values(FEATURE_CATEGORIES).forEach(category => {
      grouped[category] = this.getFeaturesByCategory(category);
    });
    
    return grouped;
  }
  
  /**
   * Search features by name or description
   */
  static searchFeatures(query: string): FeatureConfig[] {
    const lowercaseQuery = query.toLowerCase();
    return this.getAllFeatures().filter(feature => 
      feature.module.name.toLowerCase().includes(lowercaseQuery) ||
      feature.module.description.toLowerCase().includes(lowercaseQuery)
    );
  }
}

/**
 * Feature Category Metadata
 */
export const CATEGORY_METADATA = {
  [FEATURE_CATEGORIES.CORE]: {
    name: 'Core',
    nameAr: 'أساسي',
    description: 'Essential app features',
    descriptionAr: 'الميزات الأساسية للتطبيق',
    icon: 'settings',
    color: '#6b7280',
  },
  [FEATURE_CATEGORIES.PRODUCTIVITY]: {
    name: 'Productivity',
    nameAr: 'الإنتاجية',
    description: 'Time and task management',
    descriptionAr: 'إدارة الوقت والمهام',
    icon: 'trending-up',
    color: '#0ea5e9',
  },
  [FEATURE_CATEGORIES.HEALTH]: {
    name: 'Health',
    nameAr: 'الصحة',
    description: 'Health and wellness tracking',
    descriptionAr: 'تتبع الصحة والعافية',
    icon: 'heart',
    color: '#ef4444',
  },
  [FEATURE_CATEGORIES.LEARNING]: {
    name: 'Learning',
    nameAr: 'التعلم',
    description: 'Education and skill development',
    descriptionAr: 'التعليم وتطوير المهارات',
    icon: 'book-open',
    color: '#8b5cf6',
  },
  [FEATURE_CATEGORIES.SHOPPING]: {
    name: 'Shopping',
    nameAr: 'التسوق',
    description: 'Smart shopping assistance',
    descriptionAr: 'مساعدة التسوق الذكي',
    icon: 'shopping-bag',
    color: '#f59e0b',
  },
  [FEATURE_CATEGORIES.COMMUNICATION]: {
    name: 'Communication',
    nameAr: 'التواصل',
    description: 'AI chat and messaging',
    descriptionAr: 'الدردشة والرسائل الذكية',
    icon: 'message-square',
    color: '#10b981',
  },
  [FEATURE_CATEGORIES.PRIVACY]: {
    name: 'Privacy',
    nameAr: 'الخصوصية',
    description: 'Privacy and security',
    descriptionAr: 'الخصوصية والأمان',
    icon: 'shield',
    color: '#6366f1',
  },
  [FEATURE_CATEGORIES.CAMERA]: {
    name: 'Camera & AR',
    nameAr: 'الكاميرا والواقع المعزز',
    description: 'Camera and augmented reality',
    descriptionAr: 'الكاميرا والواقع المعزز',
    icon: 'camera',
    color: '#ec4899',
  },
} as const;
