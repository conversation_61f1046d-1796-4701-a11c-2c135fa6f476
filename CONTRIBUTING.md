# دليل المساهمة في LifeAI Assistant

نشكرك على اهتمامك بالمساهمة في مشروع LifeAI Assistant! 🎉

## 📋 جدول المحتويات

- [قواعد السلوك](#قواعد-السلوك)
- [كيفية المساهمة](#كيفية-المساهمة)
- [إعداد بيئة التطوير](#إعداد-بيئة-التطوير)
- [معايير الكود](#معايير-الكود)
- [عملية المراجعة](#عملية-المراجعة)
- [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
- [اقتراح الميزات](#اقتراح-الميزات)

## 🤝 قواعد السلوك

نحن ملتزمون بتوفير بيئة ترحيبية وشاملة للجميع. يرجى قراءة [قواعد السلوك](CODE_OF_CONDUCT.md) قبل المساهمة.

## 🚀 كيفية المساهمة

### 1. Fork المشروع
```bash
# انسخ المشروع إلى حسابك
git clone https://github.com/your-username/lifeai-assistant.git
cd lifeai-assistant
```

### 2. إنشاء فرع جديد
```bash
# أنشئ فرع جديد للميزة أو الإصلاح
git checkout -b feature/amazing-feature
# أو
git checkout -b fix/bug-description
```

### 3. تطبيق التغييرات
- اتبع [معايير الكود](#معايير-الكود)
- أضف اختبارات للكود الجديد
- تأكد من أن جميع الاختبارات تمر
- حدث التوثيق إذا لزم الأمر

### 4. Commit التغييرات
```bash
# استخدم رسائل commit واضحة ووصفية
git commit -m "feat: add amazing new feature"
git commit -m "fix: resolve issue with authentication"
git commit -m "docs: update README with new instructions"
```

### 5. Push ورفع Pull Request
```bash
git push origin feature/amazing-feature
```

## 🛠️ إعداد بيئة التطوير

### المتطلبات الأساسية
- Node.js >= 18.0.0
- npm >= 8.0.0
- React Native CLI
- Android Studio (للتطوير على Android)
- Xcode (للتطوير على iOS)

### خطوات الإعداد
```bash
# 1. تثبيت التبعيات
npm install

# 2. إعداد Husky
npm run prepare

# 3. تشغيل الاختبارات
npm test

# 4. فحص جودة الكود
npm run lint
npm run type-check

# 5. تشغيل التطبيق
npm run android  # أو npm run ios
```

## 📝 معايير الكود

### TypeScript
- استخدم TypeScript لجميع الملفات الجديدة
- حدد الأنواع بوضوح
- تجنب استخدام `any` إلا عند الضرورة القصوى

### تسمية الملفات والمجلدات
- استخدم PascalCase للمكونات: `UserProfile.tsx`
- استخدم camelCase للوظائف والمتغيرات: `getUserData`
- استخدم kebab-case للمجلدات: `user-profile`

### بنية المكونات
```typescript
// مثال على بنية مكون صحيحة
import React from 'react';
import { View, Text } from 'react-native';
import { useTranslation } from 'react-i18next';

interface Props {
  title: string;
  onPress?: () => void;
}

const MyComponent: React.FC<Props> = ({ title, onPress }) => {
  const { t } = useTranslation();

  return (
    <View>
      <Text>{title}</Text>
    </View>
  );
};

export default MyComponent;
```

### ESLint و Prettier
- يجب أن يمر الكود من فحص ESLint بدون أخطاء
- استخدم Prettier لتنسيق الكود
- تشغل هذه الأدوات تلقائياً عند الـ commit

### الاختبارات
```typescript
// مثال على اختبار مكون
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import MyComponent from '../MyComponent';

describe('MyComponent', () => {
  it('renders correctly', () => {
    const { getByText } = render(<MyComponent title="Test Title" />);
    expect(getByText('Test Title')).toBeTruthy();
  });

  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <MyComponent title="Test" onPress={mockOnPress} />
    );
    
    fireEvent.press(getByText('Test'));
    expect(mockOnPress).toHaveBeenCalled();
  });
});
```

## 🔍 عملية المراجعة

### قبل رفع Pull Request
- [ ] تأكد من أن جميع الاختبارات تمر
- [ ] فحص ESLint و TypeScript بدون أخطاء
- [ ] تحديث التوثيق إذا لزم الأمر
- [ ] إضافة اختبارات للكود الجديد
- [ ] اختبار التطبيق على كلا المنصتين (iOS/Android)

### معايير المراجعة
- **الوظيفة**: هل يعمل الكود كما هو متوقع؟
- **الأداء**: هل يؤثر على أداء التطبيق؟
- **الأمان**: هل يتبع أفضل ممارسات الأمان؟
- **إمكانية الصيانة**: هل الكود قابل للقراءة والفهم؟
- **التوافق**: هل يعمل على جميع المنصات المدعومة؟

## 🐛 الإبلاغ عن الأخطاء

### قبل الإبلاغ
- تأكد من أن المشكلة لم يتم الإبلاغ عنها مسبقاً
- جرب إعادة إنتاج المشكلة
- تحقق من أنك تستخدم أحدث إصدار

### معلومات مطلوبة
```markdown
**وصف المشكلة**
وصف واضح ومختصر للمشكلة.

**خطوات إعادة الإنتاج**
1. اذهب إلى '...'
2. اضغط على '...'
3. انتقل إلى '...'
4. شاهد الخطأ

**السلوك المتوقع**
وصف واضح لما كنت تتوقع حدوثه.

**لقطات الشاشة**
إذا كان ذلك مناسباً، أضف لقطات شاشة لتوضيح المشكلة.

**معلومات البيئة:**
- النظام: [iOS/Android]
- إصدار النظام: [مثل iOS 15.0]
- إصدار التطبيق: [مثل 1.0.0]
- الجهاز: [مثل iPhone 12]
```

## 💡 اقتراح الميزات

### قبل الاقتراح
- تأكد من أن الميزة لم يتم اقتراحها مسبقاً
- فكر في كيفية تناسب الميزة مع رؤية التطبيق
- اعتبر تأثير الميزة على الأداء وتجربة المستخدم

### قالب اقتراح الميزة
```markdown
**هل اقتراحك مرتبط بمشكلة؟**
وصف واضح ومختصر للمشكلة. مثال: أشعر بالإحباط عندما [...]

**وصف الحل المقترح**
وصف واضح ومختصر لما تريد حدوثه.

**وصف البدائل**
وصف واضح ومختصر لأي حلول أو ميزات بديلة فكرت فيها.

**سياق إضافي**
أضف أي سياق أو لقطات شاشة أخرى حول طلب الميزة هنا.
```

## 📚 الموارد المفيدة

- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [React Navigation](https://reactnavigation.org/docs/getting-started)
- [React Query](https://tanstack.com/query/latest)
- [Redux Toolkit](https://redux-toolkit.js.org/)

## 🙏 شكر خاص

نشكر جميع المساهمين الذين يساعدون في تحسين LifeAI Assistant!

---

إذا كان لديك أي أسئلة، لا تتردد في فتح issue أو التواصل معنا على <EMAIL>
