/**
 * Smart Planner Feature Module
 * 
 * إدارة الوقت والمهام الذكية مع الذكاء الاصطناعي
 * - تخطيط المهام التلقائي
 * - تحليل الإنتاجية
 * - تذكيرات ذكية
 * - تحسين الجدولة
 */

// Domain Layer Exports
export * from './domain/entities';
export * from './domain/usecases';
export type { TaskRepository, ScheduleRepository } from './domain/repositories';

// Data Layer Exports
export { TaskRepositoryImpl } from './data/repositories/TaskRepositoryImpl';
export { ScheduleRepositoryImpl } from './data/repositories/ScheduleRepositoryImpl';

// Presentation Layer Exports
export { default as SmartPlannerScreen } from './presentation/screens/SmartPlannerScreen';
export { default as TaskListScreen } from './presentation/screens/TaskListScreen';
export { default as ScheduleScreen } from './presentation/screens/ScheduleScreen';
export { default as AnalyticsScreen } from './presentation/screens/AnalyticsScreen';

// Components
export { default as TaskCard } from './presentation/components/TaskCard';
export { default as ScheduleCalendar } from './presentation/components/ScheduleCalendar';
export { default as ProductivityChart } from './presentation/components/ProductivityChart';
export { default as SmartSuggestions } from './presentation/components/SmartSuggestions';

// Hooks
export { useTaskManager } from './presentation/hooks/useTaskManager';
export { useScheduleOptimizer } from './presentation/hooks/useScheduleOptimizer';
export { useProductivityAnalytics } from './presentation/hooks/useProductivityAnalytics';

// Store
export { smartPlannerSlice, smartPlannerActions } from './presentation/store/smartPlannerSlice';
export type { SmartPlannerState } from './presentation/store/smartPlannerSlice';

// Infrastructure
export { AISchedulingService } from './infrastructure/services/AISchedulingService';
export { ProductivityAnalyticsService } from './infrastructure/services/ProductivityAnalyticsService';

// Types
export type {
  Task,
  Schedule,
  ProductivityMetrics,
  SmartSuggestion,
  TaskPriority,
  TaskStatus,
  ScheduleConflict,
} from './domain/entities';

// Constants
export { SMART_PLANNER_ROUTES } from './infrastructure/config/routes';
export { TASK_PRIORITIES, TASK_STATUSES } from './infrastructure/config/constants';

/**
 * Feature Module Configuration
 */
export const SmartPlannerModule = {
  name: 'SmartPlanner',
  version: '1.0.0',
  description: 'AI-powered time and task management',
  dependencies: ['ai', 'notifications', 'analytics'],
  routes: [
    'SmartPlannerScreen',
    'TaskListScreen',
    'ScheduleScreen',
    'AnalyticsScreen',
  ],
} as const;
