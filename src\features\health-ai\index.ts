/**
 * Health AI Feature Module
 * 
 * مساعد صحي ذكي مدعوم بالذكاء الاصطناعي
 * - تتبع الصحة العامة
 * - نصائح صحية مخصصة
 * - تحليل الأعراض
 * - تذكيرات الأدوية
 * - تتبع اللياقة البدنية
 */

// Domain Layer Exports
export * from './domain/entities';
export * from './domain/usecases';
export type { 
  HealthRepository, 
  MedicationRepository, 
  FitnessRepository,
  SymptomRepository 
} from './domain/repositories';

// Data Layer Exports
export { HealthRepositoryImpl } from './data/repositories/HealthRepositoryImpl';
export { MedicationRepositoryImpl } from './data/repositories/MedicationRepositoryImpl';
export { FitnessRepositoryImpl } from './data/repositories/FitnessRepositoryImpl';

// Presentation Layer Exports
export { default as HealthDashboardScreen } from './presentation/screens/HealthDashboardScreen';
export { default as SymptomCheckerScreen } from './presentation/screens/SymptomCheckerScreen';
export { default as MedicationScreen } from './presentation/screens/MedicationScreen';
export { default as FitnessTrackingScreen } from './presentation/screens/FitnessTrackingScreen';
export { default as HealthInsightsScreen } from './presentation/screens/HealthInsightsScreen';

// Components
export { default as HealthMetricCard } from './presentation/components/HealthMetricCard';
export { default as SymptomChecker } from './presentation/components/SymptomChecker';
export { default as MedicationReminder } from './presentation/components/MedicationReminder';
export { default as FitnessTracker } from './presentation/components/FitnessTracker';
export { default as HealthChart } from './presentation/components/HealthChart';
export { default as AIHealthAssistant } from './presentation/components/AIHealthAssistant';

// Hooks
export { useHealthTracking } from './presentation/hooks/useHealthTracking';
export { useSymptomAnalysis } from './presentation/hooks/useSymptomAnalysis';
export { useMedicationReminders } from './presentation/hooks/useMedicationReminders';
export { useFitnessGoals } from './presentation/hooks/useFitnessGoals';
export { useHealthInsights } from './presentation/hooks/useHealthInsights';

// Store
export { healthAISlice, healthAIActions } from './presentation/store/healthAISlice';
export type { HealthAIState } from './presentation/store/healthAISlice';

// Infrastructure
export { AIHealthAnalysisService } from './infrastructure/services/AIHealthAnalysisService';
export { HealthDataSyncService } from './infrastructure/services/HealthDataSyncService';
export { MedicationReminderService } from './infrastructure/services/MedicationReminderService';

// Types
export type {
  HealthMetric,
  HealthProfile,
  Symptom,
  Medication,
  FitnessActivity,
  HealthGoal,
  HealthInsight,
  VitalSigns,
  HealthRecord,
  AIHealthRecommendation,
} from './domain/entities';

// Constants
export { HEALTH_AI_ROUTES } from './infrastructure/config/routes';
export { HEALTH_METRICS, SYMPTOM_CATEGORIES } from './infrastructure/config/constants';

/**
 * Feature Module Configuration
 */
export const HealthAIModule = {
  name: 'HealthAI',
  version: '1.0.0',
  description: 'AI-powered health assistant and tracking',
  dependencies: ['ai', 'notifications', 'sensors', 'camera'],
  permissions: ['health', 'camera', 'location'],
  routes: [
    'HealthDashboardScreen',
    'SymptomCheckerScreen',
    'MedicationScreen',
    'FitnessTrackingScreen',
    'HealthInsightsScreen',
  ],
} as const;
