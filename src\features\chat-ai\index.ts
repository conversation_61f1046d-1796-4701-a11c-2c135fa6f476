/**
 * Chat AI Feature Module
 * 
 * دردشة تفاعلية ذكية مع الذكاء الاصطناعي المتقدم
 * - محادثات متعددة السياق
 * - دعم الصوت والصورة
 * - ذاكرة طويلة المدى
 * - شخصيات AI متنوعة
 * - تحليل المشاعر
 */

// Domain Layer Exports
export * from './domain/entities';
export * from './domain/usecases';
export type { 
  ChatRepository, 
  ConversationRepository, 
  MessageRepository,
  AIPersonalityRepository 
} from './domain/repositories';

// Data Layer Exports
export { ChatRepositoryImpl } from './data/repositories/ChatRepositoryImpl';
export { ConversationRepositoryImpl } from './data/repositories/ConversationRepositoryImpl';
export { MessageRepositoryImpl } from './data/repositories/MessageRepositoryImpl';

// Presentation Layer Exports
export { default as ChatDashboardScreen } from './presentation/screens/ChatDashboardScreen';
export { default as ConversationScreen } from './presentation/screens/ConversationScreen';
export { default as AIPersonalitiesScreen } from './presentation/screens/AIPersonalitiesScreen';
export { default as ChatHistoryScreen } from './presentation/screens/ChatHistoryScreen';
export { default as VoiceChatScreen } from './presentation/screens/VoiceChatScreen';

// Components
export { default as MessageBubble } from './presentation/components/MessageBubble';
export { default as ChatInput } from './presentation/components/ChatInput';
export { default as ConversationList } from './presentation/components/ConversationList';
export { default as AIPersonalityCard } from './presentation/components/AIPersonalityCard';
export { default as VoiceRecorder } from './presentation/components/VoiceRecorder';
export { default as ImageAnalyzer } from './presentation/components/ImageAnalyzer';
export { default as TypingIndicator } from './presentation/components/TypingIndicator';
export { default as MessageReactions } from './presentation/components/MessageReactions';

// Hooks
export { useChat } from './presentation/hooks/useChat';
export { useConversations } from './presentation/hooks/useConversations';
export { useVoiceChat } from './presentation/hooks/useVoiceChat';
export { useMessageStreaming } from './presentation/hooks/useMessageStreaming';
export { useAIPersonalities } from './presentation/hooks/useAIPersonalities';
export { useChatAnalytics } from './presentation/hooks/useChatAnalytics';

// Store
export { chatAISlice, chatAIActions } from './presentation/store/chatAISlice';
export type { ChatAIState } from './presentation/store/chatAISlice';

// Infrastructure
export { AdvancedAIService } from './infrastructure/services/AdvancedAIService';
export { ConversationMemoryService } from './infrastructure/services/ConversationMemoryService';
export { SentimentAnalysisService } from './infrastructure/services/SentimentAnalysisService';
export { VoiceProcessingService } from './infrastructure/services/VoiceProcessingService';

// Types
export type {
  Message,
  Conversation,
  AIPersonality,
  ChatContext,
  MessageType,
  VoiceMessage,
  ImageMessage,
  MessageReaction,
  ConversationSummary,
  ChatAnalytics,
  SentimentAnalysis,
} from './domain/entities';

// Constants
export { CHAT_AI_ROUTES } from './infrastructure/config/routes';
export { MESSAGE_TYPES, AI_PERSONALITIES } from './infrastructure/config/constants';

/**
 * Feature Module Configuration
 */
export const ChatAIModule = {
  name: 'ChatAI',
  version: '1.0.0',
  description: 'Advanced AI chat with multi-modal support',
  dependencies: ['ai', 'voice', 'camera', 'analytics'],
  permissions: ['microphone', 'camera', 'storage'],
  routes: [
    'ChatDashboardScreen',
    'ConversationScreen',
    'AIPersonalitiesScreen',
    'ChatHistoryScreen',
    'VoiceChatScreen',
  ],
} as const;
