/**
 * Core Module - Application Foundation
 * 
 * This module contains the core functionality that powers the entire application:
 * - API layer and HTTP client
 * - Storage management (local, secure, cache)
 * - Navigation system
 * - Application constants and configuration
 * - Error handling and logging
 * - Performance monitoring
 */

// API layer
export * from './api';

// Storage management
export * from './storage';

// Navigation system
export * from './navigation';

// Constants and configuration
export * from './constants';

// Error handling
export { ErrorBoundary } from './error/ErrorBoundary';
export { ErrorHandler } from './error/ErrorHandler';
export { Logger } from './error/Logger';

// Performance monitoring
export { PerformanceMonitor } from './performance/PerformanceMonitor';
export { MetricsCollector } from './performance/MetricsCollector';

// Utilities
export { DeviceInfo } from './utils/DeviceInfo';
export { NetworkManager } from './utils/NetworkManager';
export { PermissionManager } from './utils/PermissionManager';

// Types
export type {
  ApiResponse,
  ApiError,
  StorageOptions,
  NavigationState,
  AppConfig,
  LogLevel,
  PerformanceMetrics,
} from './types';
