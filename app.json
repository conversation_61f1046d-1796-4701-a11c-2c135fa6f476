{"name": "LifeAIAssistant", "displayName": "LifeAI Assistant", "description": "AI-powered mobile app for life management", "version": "1.0.0", "buildNumber": "1", "bundleId": "com.lifeai.assistant", "packageName": "com.lifeai.assistant", "orientation": "portrait", "icon": "./assets/icon.png", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#0ea5e9"}, "platforms": ["ios", "android"], "privacy": "public", "sdkVersion": "49.0.0", "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.lifeai.assistant", "buildNumber": "1", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to capture images for AI analysis.", "NSMicrophoneUsageDescription": "This app uses the microphone to record voice messages.", "NSPhotoLibraryUsageDescription": "This app accesses your photo library to select images for AI analysis.", "NSFaceIDUsageDescription": "This app uses Face ID for secure authentication.", "CFBundleLocalizations": ["en", "ar"]}}, "android": {"package": "com.lifeai.assistant", "versionCode": 1, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.VIBRATE", "android.permission.USE_FINGERPRINT", "android.permission.USE_BIOMETRIC"]}}