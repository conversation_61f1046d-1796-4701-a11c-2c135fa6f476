/**
 * Chat Feature Module
 * 
 * This module handles all chat and AI conversation functionality including:
 * - Real-time messaging with AI
 * - Conversation history
 * - Message types (text, voice, image)
 * - Chat sessions management
 * - AI response streaming
 * - Context awareness
 */

// Components
export { default as ChatScreen } from './components/ChatScreen';
export { default as MessageBubble } from './components/MessageBubble';
export { default as ChatInput } from './components/ChatInput';
export { default as ConversationList } from './components/ConversationList';
export { default as VoiceRecorder } from './components/VoiceRecorder';
export { default as ImagePicker } from './components/ImagePicker';
export { default as TypingIndicator } from './components/TypingIndicator';

// Hooks
export { useChat } from './hooks/useChat';
export { useChatHistory } from './hooks/useChatHistory';
export { useVoiceRecording } from './hooks/useVoiceRecording';
export { useMessageStreaming } from './hooks/useMessageStreaming';
export { useChatContext } from './hooks/useChatContext';

// Services
export { ChatService } from './services/ChatService';
export { AIService } from './services/AIService';
export { VoiceService } from './services/VoiceService';
export { ImageAnalysisService } from './services/ImageAnalysisService';

// Types
export type {
  Message,
  Conversation,
  ChatState,
  MessageType,
  AIResponse,
  VoiceMessage,
  ImageMessage,
  ChatContext,
} from './types';

// Constants
export { CHAT_ROUTES } from './constants/routes';
export { MESSAGE_TYPES } from './constants/messageTypes';
export { AI_MODELS } from './constants/aiModels';

// Store/State management
export { chatSlice, chatActions } from './store/chatSlice';
export type { ChatSliceState } from './store/chatSlice';
