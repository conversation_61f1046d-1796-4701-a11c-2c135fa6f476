/**
 * DeepSeek Provider Implementation
 * 
 * تنفيذ مقدم خدمة DeepSeek مع دعم النماذج المتخصصة
 */

import { 
  AIProvider, 
  AIRequest, 
  AIResponse, 
  StreamingResponse, 
  ProviderType,
  AIError,
  ModelConfig 
} from '../types';
import { MODEL_CONFIGS } from '../config/AIConfig';
import { Logger } from '@/core/error/Logger';

interface DeepSeekConfig {
  apiKey: string;
  baseURL?: string;
  timeout?: number;
}

interface DeepSeekMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface DeepSeekRequestBody {
  model: string;
  messages: DeepSeekMessage[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string[];
  stream?: boolean;
}

interface DeepSeekResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message?: {
      role: string;
      content: string;
    };
    delta?: {
      role?: string;
      content?: string;
    };
    finish_reason: string | null;
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * DeepSeek Provider Class
 */
export class DeepSeekProvider implements AIProvider {
  name: ProviderType = 'deepseek';
  apiKey: string;
  baseURL: string;
  timeout: number;
  models: Record<string, ModelConfig>;
  rateLimit = {
    requestsPerMinute: 100,
    tokensPerMinute: 200000,
  };

  private requestCount = 0;
  private lastResetTime = Date.now();

  constructor(config: DeepSeekConfig) {
    this.apiKey = config.apiKey;
    this.baseURL = config.baseURL || 'https://api.deepseek.com/v1';
    this.timeout = config.timeout || 30000;
    this.models = MODEL_CONFIGS.deepseek;
  }

  /**
   * Send AI request to DeepSeek
   */
  async sendRequest(request: AIRequest): Promise<AIResponse> {
    try {
      const requestBody = this.buildRequestBody(request);
      const response = await this.makeAPICall('/chat/completions', requestBody);
      
      return this.parseResponse(response, request);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Send streaming AI request to DeepSeek
   */
  async* sendStreamingRequest(request: AIRequest): AsyncGenerator<StreamingResponse, void, unknown> {
    try {
      const requestBody = this.buildRequestBody(request, true);
      const response = await this.makeStreamingAPICall('/chat/completions', requestBody);
      
      if (!response.body) {
        throw new Error('No response body for streaming');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let responseId = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) break;
          
          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() === '') continue;
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              
              if (data === '[DONE]') {
                return;
              }

              try {
                const parsed: DeepSeekResponse = JSON.parse(data);
                responseId = parsed.id;
                
                const choice = parsed.choices[0];
                if (choice?.delta?.content) {
                  yield {
                    id: responseId,
                    delta: choice.delta.content,
                    isComplete: false,
                  };
                }

                if (choice?.finish_reason) {
                  yield {
                    id: responseId,
                    delta: '',
                    isComplete: true,
                    usage: parsed.usage,
                  };
                  return;
                }
              } catch (parseError) {
                Logger.warn('Failed to parse DeepSeek streaming response:', parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Health check for DeepSeek service
   */
  async healthCheck(): Promise<boolean> {
    try {
      // DeepSeek doesn't have a models endpoint, so we'll test with a simple request
      const testRequest: AIRequest = {
        messages: [{ role: 'user', content: 'Hello', timestamp: new Date() }],
        model: 'deepseek-chat',
        maxTokens: 10,
        temperature: 0,
      };
      
      await this.sendRequest(testRequest);
      return true;
    } catch (error) {
      Logger.warn('DeepSeek health check failed:', error);
      return false;
    }
  }

  /**
   * Validate API key format
   */
  async validateApiKey(): Promise<boolean> {
    if (!this.apiKey || !this.apiKey.startsWith('sk-')) {
      return false;
    }

    try {
      return await this.healthCheck();
    } catch {
      return false;
    }
  }

  /**
   * Get available models
   */
  async getAvailableModels(): Promise<string[]> {
    // DeepSeek has fixed models
    return Object.keys(this.models);
  }

  /**
   * Estimate tokens for text
   */
  estimateTokens(text: string): number {
    // Similar estimation to OpenAI
    return Math.ceil(text.length / 4);
  }

  // Private helper methods

  private buildRequestBody(request: AIRequest, stream = false): DeepSeekRequestBody {
    const model = request.model || 'deepseek-chat';
    const modelConfig = this.models[model] || this.models['deepseek-chat'];

    return {
      model,
      messages: request.messages.map(msg => ({
        role: msg.role,
        content: msg.content,
      })),
      max_tokens: request.maxTokens || modelConfig.maxTokens,
      temperature: request.temperature ?? modelConfig.temperature,
      top_p: modelConfig.topP,
      frequency_penalty: modelConfig.frequencyPenalty,
      presence_penalty: modelConfig.presencePenalty,
      stop: modelConfig.stopSequences,
      stream,
    };
  }

  private async makeAPICall(endpoint: string, body: any): Promise<any> {
    const url = `${this.baseURL}${endpoint}`;
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
    };

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
      signal: AbortSignal.timeout(this.timeout),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`DeepSeek API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    return response.json();
  }

  private async makeStreamingAPICall(endpoint: string, body: any): Promise<Response> {
    const url = `${this.baseURL}${endpoint}`;
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
    };

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
      signal: AbortSignal.timeout(this.timeout),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`DeepSeek API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    return response;
  }

  private parseResponse(response: DeepSeekResponse, request: AIRequest): AIResponse {
    const choice = response.choices[0];
    if (!choice?.message) {
      throw new Error('Invalid response format from DeepSeek');
    }

    return {
      id: response.id,
      content: choice.message.content,
      model: response.model,
      usage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0,
      },
      finishReason: choice.finish_reason as any || 'stop',
      timestamp: new Date(),
      metadata: {
        provider: 'deepseek',
        requestId: request.sessionId,
      },
    };
  }

  private handleError(error: any): AIError {
    const aiError = new Error(error.message || 'DeepSeek provider error') as AIError;
    aiError.code = error.code || 'DEEPSEEK_ERROR';
    aiError.provider = 'deepseek';
    aiError.retryable = error.status >= 500 || error.code === 'ECONNRESET';
    aiError.statusCode = error.status;
    aiError.details = error;
    
    return aiError;
  }
}
