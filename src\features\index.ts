/**
 * Features Module - Main Export
 * 
 * This module exports all the main features of the LifeAI Assistant application.
 * Each feature is designed as a self-contained module with its own components,
 * hooks, services, and types.
 */

// Authentication feature
export * from './auth';

// Chat/AI conversation feature
export * from './chat';

// User profile management
export * from './profile';

// Application settings
export * from './settings';

// Dashboard/Home feature
export * from './dashboard';

/**
 * Feature registry for dynamic loading and navigation
 */
export const FEATURES = {
  AUTH: 'auth',
  CHAT: 'chat',
  PROFILE: 'profile',
  SETTINGS: 'settings',
  DASHBOARD: 'dashboard',
} as const;

export type FeatureType = typeof FEATURES[keyof typeof FEATURES];
