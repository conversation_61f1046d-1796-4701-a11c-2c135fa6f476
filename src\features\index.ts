/**
 * Features Module - Main Export
 *
 * This module exports all the main features of the LifeAI Assistant application.
 * Each feature is designed as a self-contained module following Clean Architecture
 * principles with clear separation of concerns.
 */

// Core Features
export * from './auth';
export * from './profile';
export * from './settings';
export * from './dashboard';

// AI-Powered Features
export * from './smart-planner';
export * from './health-ai';
export * from './learning-ai';
export * from './smart-shopping';
export * from './chat-ai';
export * from './privacy-local-ai';
export * from './camera-ar';

/**
 * Feature registry for dynamic loading and navigation
 */
export const FEATURES = {
  // Core Features
  AUTH: 'auth',
  PROFILE: 'profile',
  SETTINGS: 'settings',
  DASHBOARD: 'dashboard',

  // AI-Powered Features
  SMART_PLANNER: 'smart-planner',
  HEALTH_AI: 'health-ai',
  LEARNING_AI: 'learning-ai',
  SMART_SHOPPING: 'smart-shopping',
  CHAT_AI: 'chat-ai',
  PRIVACY_LOCAL_AI: 'privacy-local-ai',
  CAMERA_AR: 'camera-ar',
} as const;

export type FeatureType = typeof FEATURES[keyof typeof FEATURES];

/**
 * Feature Categories for UI Organization
 */
export const FEATURE_CATEGORIES = {
  CORE: 'core',
  PRODUCTIVITY: 'productivity',
  HEALTH: 'health',
  LEARNING: 'learning',
  SHOPPING: 'shopping',
  COMMUNICATION: 'communication',
  PRIVACY: 'privacy',
  CAMERA: 'camera',
} as const;

/**
 * Feature to Category Mapping
 */
export const FEATURE_CATEGORY_MAP = {
  [FEATURES.AUTH]: FEATURE_CATEGORIES.CORE,
  [FEATURES.PROFILE]: FEATURE_CATEGORIES.CORE,
  [FEATURES.SETTINGS]: FEATURE_CATEGORIES.CORE,
  [FEATURES.DASHBOARD]: FEATURE_CATEGORIES.CORE,
  [FEATURES.SMART_PLANNER]: FEATURE_CATEGORIES.PRODUCTIVITY,
  [FEATURES.HEALTH_AI]: FEATURE_CATEGORIES.HEALTH,
  [FEATURES.LEARNING_AI]: FEATURE_CATEGORIES.LEARNING,
  [FEATURES.SMART_SHOPPING]: FEATURE_CATEGORIES.SHOPPING,
  [FEATURES.CHAT_AI]: FEATURE_CATEGORIES.COMMUNICATION,
  [FEATURES.PRIVACY_LOCAL_AI]: FEATURE_CATEGORIES.PRIVACY,
  [FEATURES.CAMERA_AR]: FEATURE_CATEGORIES.CAMERA,
} as const;
