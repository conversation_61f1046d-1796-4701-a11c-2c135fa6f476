/**
 * Authentication Feature Module
 * 
 * This module handles all authentication-related functionality including:
 * - User login/logout
 * - Registration
 * - Password recovery
 * - Biometric authentication
 * - Session management
 * - Token refresh
 */

// Components
export { default as LoginScreen } from './components/LoginScreen';
export { default as RegisterScreen } from './components/RegisterScreen';
export { default as ForgotPasswordScreen } from './components/ForgotPasswordScreen';
export { default as BiometricSetupScreen } from './components/BiometricSetupScreen';

// Hooks
export { useAuth } from './hooks/useAuth';
export { useAuthForm } from './hooks/useAuthForm';
export { useBiometric } from './hooks/useBiometric';

// Services
export { AuthService } from './services/AuthService';
export { BiometricService } from './services/BiometricService';
export { TokenService } from './services/TokenService';

// Types
export type {
  User,
  AuthState,
  LoginCredentials,
  RegisterData,
  AuthError,
  BiometricType,
} from './types';

// Constants
export { AUTH_ROUTES } from './constants/routes';
export { AUTH_ERRORS } from './constants/errors';

// Store/State management
export { authSlice, authActions } from './store/authSlice';
export type { AuthSliceState } from './store/authSlice';
