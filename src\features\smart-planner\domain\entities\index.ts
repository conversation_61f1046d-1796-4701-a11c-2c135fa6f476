/**
 * Smart Planner Domain Entities
 * 
 * Business entities for the Smart Planner feature
 */

/**
 * Task Priority Levels
 */
export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';

/**
 * Task Status Types
 */
export type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'overdue';

/**
 * Task Category Types
 */
export type TaskCategory = 'work' | 'personal' | 'health' | 'learning' | 'shopping' | 'social';

/**
 * Task Entity
 */
export interface Task {
  id: string;
  title: string;
  description?: string;
  category: TaskCategory;
  priority: TaskPriority;
  status: TaskStatus;
  dueDate?: Date;
  estimatedDuration: number; // in minutes
  actualDuration?: number; // in minutes
  tags: string[];
  subtasks: SubTask[];
  dependencies: string[]; // Task IDs
  location?: string;
  reminders: Reminder[];
  aiSuggestions: SmartSuggestion[];
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

/**
 * SubTask Entity
 */
export interface SubTask {
  id: string;
  title: string;
  isCompleted: boolean;
  estimatedDuration: number;
  actualDuration?: number;
  createdAt: Date;
  completedAt?: Date;
}

/**
 * Schedule Entity
 */
export interface Schedule {
  id: string;
  userId: string;
  date: Date;
  timeSlots: TimeSlot[];
  conflicts: ScheduleConflict[];
  optimizationScore: number; // 0-100
  aiOptimized: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Time Slot Entity
 */
export interface TimeSlot {
  id: string;
  startTime: Date;
  endTime: Date;
  taskId?: string;
  type: 'task' | 'break' | 'meeting' | 'free' | 'blocked';
  title: string;
  description?: string;
  isFlexible: boolean;
  energyLevel: 'low' | 'medium' | 'high'; // User's energy level for this time
}

/**
 * Schedule Conflict Entity
 */
export interface ScheduleConflict {
  id: string;
  type: 'overlap' | 'overload' | 'dependency' | 'deadline';
  severity: 'low' | 'medium' | 'high';
  description: string;
  affectedTimeSlots: string[];
  suggestedResolution: string;
  autoResolvable: boolean;
}

/**
 * Reminder Entity
 */
export interface Reminder {
  id: string;
  taskId: string;
  type: 'notification' | 'email' | 'sms';
  triggerTime: Date;
  message: string;
  isRecurring: boolean;
  recurringPattern?: RecurringPattern;
  isActive: boolean;
  createdAt: Date;
}

/**
 * Recurring Pattern
 */
export interface RecurringPattern {
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number; // Every X days/weeks/months/years
  daysOfWeek?: number[]; // 0-6 (Sunday-Saturday)
  endDate?: Date;
  maxOccurrences?: number;
}

/**
 * Smart Suggestion Entity
 */
export interface SmartSuggestion {
  id: string;
  type: 'scheduling' | 'priority' | 'duration' | 'category' | 'break';
  title: string;
  description: string;
  confidence: number; // 0-1
  reasoning: string;
  actionable: boolean;
  action?: {
    type: string;
    payload: any;
  };
  createdAt: Date;
  appliedAt?: Date;
  dismissed?: boolean;
}

/**
 * Productivity Metrics Entity
 */
export interface ProductivityMetrics {
  id: string;
  userId: string;
  date: Date;
  tasksCompleted: number;
  tasksPlanned: number;
  completionRate: number; // 0-1
  averageTaskDuration: number; // in minutes
  focusTime: number; // in minutes
  breakTime: number; // in minutes
  productivityScore: number; // 0-100
  energyLevels: EnergyLevel[];
  distractions: Distraction[];
  insights: ProductivityInsight[];
  createdAt: Date;
}

/**
 * Energy Level Tracking
 */
export interface EnergyLevel {
  time: Date;
  level: number; // 1-10
  activity: string;
  mood?: string;
}

/**
 * Distraction Tracking
 */
export interface Distraction {
  id: string;
  type: 'notification' | 'interruption' | 'procrastination';
  duration: number; // in minutes
  source: string;
  impact: 'low' | 'medium' | 'high';
  timestamp: Date;
}

/**
 * Productivity Insight
 */
export interface ProductivityInsight {
  id: string;
  type: 'pattern' | 'recommendation' | 'achievement' | 'warning';
  title: string;
  description: string;
  data: any;
  actionable: boolean;
  priority: 'low' | 'medium' | 'high';
  createdAt: Date;
}
