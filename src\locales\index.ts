/**
 * Localization Module - Internationalization (i18n)
 * 
 * This module handles all localization and internationalization features:
 * - Multi-language support (Arabic, English)
 * - RTL (Right-to-Left) layout support
 * - Date and number formatting
 * - Currency and locale-specific formatting
 * - Dynamic language switching
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { getLocales } from 'react-native-localize';

// Language resources
import enTranslations from './en';
import arTranslations from './ar';

// Configuration
import { I18N_CONFIG } from '@/core/constants/config';

/**
 * Language resources
 */
const resources = {
  en: {
    translation: enTranslations,
  },
  ar: {
    translation: arTranslations,
  },
};

/**
 * Detect device language
 */
const getDeviceLanguage = (): string => {
  const locales = getLocales();
  const deviceLanguage = locales[0]?.languageCode || I18N_CONFIG.DEFAULT_LANGUAGE;
  
  // Check if device language is supported
  if (I18N_CONFIG.SUPPORTED_LANGUAGES.includes(deviceLanguage)) {
    return deviceLanguage;
  }
  
  return I18N_CONFIG.DEFAULT_LANGUAGE;
};

/**
 * Initialize i18n
 */
i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: getDeviceLanguage(),
    fallbackLng: I18N_CONFIG.FALLBACK_LANGUAGE,
    
    // Namespace and key separators
    nsSeparator: I18N_CONFIG.NAMESPACE_SEPARATOR,
    keySeparator: I18N_CONFIG.KEY_SEPARATOR,
    
    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
      prefix: I18N_CONFIG.INTERPOLATION.PREFIX,
      suffix: I18N_CONFIG.INTERPOLATION.SUFFIX,
    },
    
    // React options
    react: {
      useSuspense: false,
    },
    
    // Debug mode (only in development)
    debug: __DEV__,
    
    // Load path for additional namespaces
    load: 'languageOnly',
    
    // Preload languages
    preload: I18N_CONFIG.SUPPORTED_LANGUAGES,
    
    // Clean code on language change
    cleanCodeOnLangSwitch: true,
    
    // Detection options
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
  });

/**
 * Language utilities
 */
export const LanguageUtils = {
  /**
   * Get current language
   */
  getCurrentLanguage: (): string => i18n.language,
  
  /**
   * Change language
   */
  changeLanguage: async (language: string): Promise<void> => {
    if (I18N_CONFIG.SUPPORTED_LANGUAGES.includes(language)) {
      await i18n.changeLanguage(language);
    }
  },
  
  /**
   * Check if current language is RTL
   */
  isRTL: (): boolean => {
    return i18n.language === 'ar';
  },
  
  /**
   * Get supported languages
   */
  getSupportedLanguages: () => I18N_CONFIG.SUPPORTED_LANGUAGES,
  
  /**
   * Get language display name
   */
  getLanguageDisplayName: (language: string): string => {
    const displayNames: Record<string, string> = {
      en: 'English',
      ar: 'العربية',
    };
    return displayNames[language] || language;
  },
  
  /**
   * Format date according to current locale
   */
  formatDate: (date: Date, options?: Intl.DateTimeFormatOptions): string => {
    const locale = i18n.language === 'ar' ? 'ar-SA' : 'en-US';
    return new Intl.DateTimeFormat(locale, options).format(date);
  },
  
  /**
   * Format number according to current locale
   */
  formatNumber: (number: number, options?: Intl.NumberFormatOptions): string => {
    const locale = i18n.language === 'ar' ? 'ar-SA' : 'en-US';
    return new Intl.NumberFormat(locale, options).format(number);
  },
  
  /**
   * Format currency according to current locale
   */
  formatCurrency: (amount: number, currency: string = 'USD'): string => {
    const locale = i18n.language === 'ar' ? 'ar-SA' : 'en-US';
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
    }).format(amount);
  },
};

/**
 * Translation keys type safety
 */
export type TranslationKey = keyof typeof enTranslations;

/**
 * Export i18n instance
 */
export default i18n;
