{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/features/*": ["features/*"], "@/core/*": ["core/*"], "@/shared/*": ["shared/*"], "@/services/*": ["services/*"], "@/ui/*": ["ui/*"], "@/ai/*": ["ai/*"], "@/locales/*": ["locales/*"]}}, "include": ["src/**/*", "index.js", "App.tsx", "metro.config.js", "babel.config.js", "__tests__/**/*", "jest.config.js"], "exclude": ["node_modules", "android", "ios", "dist", "build", ".expo"]}