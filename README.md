# LifeAI Assistant 🤖

مساعد الحياة الذكي - تطبيق هاتف ذكي متعدد المنصات مدعوم بالذكاء الاصطناعي لإدارة الحياة اليومية

## 📱 نظرة عامة

LifeAI Assistant هو تطبيق هاتف ذكي متطور مبني باستخدام React Native + TypeScript، يوفر تجربة مستخدم متميزة مع دعم كامل للذكاء الاصطناعي والترجمة ثنائية اللغة (العربية/الإنجليزية).

## 🏗️ بنية المشروع

```
LifeAI-Assistant/
├── src/                          # المجلد الرئيسي للكود المصدري
│   ├── features/                 # الميزات الرئيسية للتطبيق
│   │   ├── auth/                # نظام المصادقة والأمان
│   │   ├── chat/                # محادثات الذكاء الاصطناعي
│   │   ├── profile/             # إدارة الملف الشخصي
│   │   ├── settings/            # إعدادات التطبيق
│   │   └── dashboard/           # لوحة التحكم الرئيسية
│   ├── core/                    # الوظائف الأساسية
│   │   ├── api/                 # طبقة API والاتصال بالخوادم
│   │   ├── storage/             # إدارة التخزين المحلي والآمن
│   │   ├── navigation/          # نظام التنقل
│   │   └── constants/           # الثوابت والتكوينات
│   ├── shared/                  # المكونات والأدوات المشتركة
│   │   ├── components/          # مكونات UI قابلة للإعادة
│   │   ├── hooks/               # React Hooks مخصصة
│   │   ├── utils/               # وظائف مساعدة
│   │   └── types/               # تعريفات TypeScript
│   ├── services/                # الخدمات الخارجية
│   │   ├── ai/                  # خدمات الذكاء الاصطناعي
│   │   ├── analytics/           # خدمات التحليلات
│   │   └── notifications/       # خدمات الإشعارات
│   ├── ui/                      # نظام التصميم
│   │   ├── theme/               # الألوان والخطوط والمظاهر
│   │   ├── tokens/              # رموز التصميم
│   │   └── styles/              # الأنماط العامة
│   ├── ai/                      # منطق الذكاء الاصطناعي
│   │   ├── models/              # نماذج الذكاء الاصطناعي
│   │   ├── prompts/             # قوالب المحادثة
│   │   └── processors/          # معالجة البيانات
│   └── locales/                 # ملفات الترجمة
│       ├── ar/                  # الترجمة العربية
│       └── en/                  # الترجمة الإنجليزية
├── __tests__/                   # ملفات الاختبار
├── android/                     # ملفات Android الأصلية
├── ios/                         # ملفات iOS الأصلية
└── docs/                        # التوثيق
```

## 🎯 الميزات الرئيسية

### 🔐 نظام المصادقة (Authentication)
- تسجيل الدخول/الخروج الآمن
- المصادقة البيومترية (بصمة الإصبع/الوجه)
- إعادة تعيين كلمة المرور
- إدارة الجلسات الآمنة

### 💬 محادثات الذكاء الاصطناعي (AI Chat)
- محادثات فورية مع الذكاء الاصطناعي
- دعم الرسائل النصية والصوتية
- تحليل الصور والمحتوى المرئي
- حفظ سجل المحادثات
- أساليب محادثة متنوعة (رسمي، ودود، عادي)

### 👤 الملف الشخصي (Profile)
- إدارة المعلومات الشخصية
- تخصيص التفضيلات
- إعدادات الخصوصية والأمان
- تحديث الصورة الشخصية

### ⚙️ الإعدادات (Settings)
- تغيير اللغة (عربي/إنجليزي)
- تبديل المظهر (فاتح/مظلم)
- إعدادات الإشعارات
- تفضيلات الذكاء الاصطناعي

## 🛠️ التقنيات المستخدمة

### الأساسية
- **React Native 0.73.2** - إطار العمل الأساسي
- **TypeScript 5.0.4** - لغة البرمجة مع دعم الأنواع
- **React Navigation 6** - نظام التنقل
- **React Native Reanimated 3** - الرسوم المتحركة

### إدارة الحالة
- **Redux Toolkit** - إدارة الحالة العامة
- **React Query** - إدارة البيانات والتخزين المؤقت
- **React Hook Form** - إدارة النماذج

### التخزين والأمان
- **React Native MMKV** - تخزين سريع وآمن
- **React Native Keychain** - تخزين البيانات الحساسة
- **AsyncStorage** - التخزين المحلي

### الترجمة والتدويل
- **react-i18next** - نظام الترجمة
- **react-native-localize** - اكتشاف اللغة المحلية

### جودة الكود
- **ESLint** - فحص جودة الكود
- **Prettier** - تنسيق الكود
- **Husky** - Git hooks للفحص قبل الالتزام
- **Jest** - اختبار الوحدة

## 🚀 البدء السريع

### المتطلبات الأساسية
- Node.js >= 18.0.0
- npm >= 8.0.0
- React Native CLI
- Android Studio (للتطوير على Android)
- Xcode (للتطوير على iOS)

### التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/lifeai-assistant.git
cd lifeai-assistant
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **تثبيت pods (iOS فقط)**
```bash
cd ios && pod install && cd ..
```

4. **إعداد Husky**
```bash
npm run prepare
```

### تشغيل التطبيق

**Android:**
```bash
npm run android
```

**iOS:**
```bash
npm run ios
```

**Metro Bundler:**
```bash
npm start
```

## 🧪 الاختبار

```bash
# تشغيل جميع الاختبارات
npm test

# تشغيل الاختبارات مع المراقبة
npm run test:watch

# تقرير التغطية
npm run test:coverage
```

## 🔧 فحص جودة الكود

```bash
# فحص ESLint
npm run lint

# إصلاح مشاكل ESLint
npm run lint:fix

# فحص Prettier
npm run prettier

# إصلاح تنسيق Prettier
npm run prettier:fix

# فحص TypeScript
npm run type-check
```

## 📁 تفاصيل بنية المجلدات

### `/src/features` - الميزات
كل ميزة تحتوي على:
- `components/` - مكونات UI خاصة بالميزة
- `hooks/` - React Hooks مخصصة
- `services/` - خدمات API
- `types/` - تعريفات TypeScript
- `store/` - إدارة الحالة (Redux slices)

### `/src/core` - الوظائف الأساسية
- `api/` - HTTP client وطبقة API
- `storage/` - إدارة التخزين المحلي
- `navigation/` - تكوين التنقل
- `constants/` - الثوابت والتكوينات

### `/src/shared` - المشتركة
- `components/` - مكونات UI قابلة للإعادة
- `hooks/` - Hooks مشتركة
- `utils/` - وظائف مساعدة
- `types/` - أنواع TypeScript مشتركة

### `/src/ui` - نظام التصميم
- `theme/` - ألوان، خطوط، مظاهر
- `tokens/` - رموز التصميم
- `styles/` - أنماط عامة

## 🌍 دعم اللغات

التطبيق يدعم:
- **العربية** - مع دعم كامل للـ RTL
- **الإنجليزية** - اللغة الافتراضية

### إضافة ترجمات جديدة
1. أضف ملف الترجمة في `/src/locales/[language]/`
2. حدث `I18N_CONFIG.SUPPORTED_LANGUAGES` في `/src/core/constants/config.ts`
3. أضف اللغة إلى `resources` في `/src/locales/index.ts`

## 🎨 نظام التصميم

### الألوان
- **Primary**: أزرق (#0ea5e9)
- **Secondary**: بنفسجي (#d946ef)
- **Success**: أخضر (#22c55e)
- **Warning**: برتقالي (#f59e0b)
- **Error**: أحمر (#ef4444)

### المظاهر
- **Light Mode**: المظهر الفاتح
- **Dark Mode**: المظهر المظلم
- **System**: يتبع إعدادات النظام

## 📱 دعم المنصات

- ✅ iOS 12.0+
- ✅ Android API 21+
- ✅ دعم RTL كامل
- ✅ إمكانية الوصول (Accessibility)

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

- 📧 البريد الإلكتروني: <EMAIL>
- 🐛 تقرير الأخطاء: [GitHub Issues](https://github.com/your-username/lifeai-assistant/issues)
- 💬 المناقشات: [GitHub Discussions](https://github.com/your-username/lifeai-assistant/discussions)

---

صُنع بـ ❤️ من فريق LifeAI
