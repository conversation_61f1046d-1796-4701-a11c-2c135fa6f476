/**
 * AI Service Module - Main Export
 * 
 * وحدة خدمات الذكاء الاصطناعي الرئيسية
 * تدعم متعدد المقدمين مع حماية API Keys وفصل كامل عن UI
 */

// Core AI Service
export { AIService } from './core/AIService';
export { AIServiceFactory } from './core/AIServiceFactory';

// Provider Implementations
export { OpenAIProvider } from './providers/OpenAIProvider';
export { DeepSeekProvider } from './providers/DeepSeekProvider';
export { AnthropicProvider } from './providers/AnthropicProvider';

// Context-Specific Services
export { PlannerAIService } from './contexts/PlannerAIService';
export { HealthAIService } from './contexts/HealthAIService';
export { LearningAIService } from './contexts/LearningAIService';
export { ChatAIService } from './contexts/ChatAIService';

// Utility Services
export { PromptTemplateService } from './utils/PromptTemplateService';
export { ResponseFormatterService } from './utils/ResponseFormatterService';
export { StreamingService } from './utils/StreamingService';

// Configuration
export { AIConfig } from './config/AIConfig';
export { ProviderConfig } from './config/ProviderConfig';

// Types
export type {
  AIProvider,
  AIRequest,
  AIResponse,
  StreamingResponse,
  ContextType,
  PromptTemplate,
  AIError,
  ProviderType,
  ModelConfig,
} from './types';

// Constants
export { AI_PROVIDERS, CONTEXT_TYPES, MODEL_CONFIGS } from './constants';

/**
 * Main AI Service Functions - Ready to Use
 */

// Import context services for direct export
import { PlannerAIService } from './contexts/PlannerAIService';
import { HealthAIService } from './contexts/HealthAIService';
import { LearningAIService } from './contexts/LearningAIService';
import { ChatAIService } from './contexts/ChatAIService';

/**
 * Smart Planner AI Functions
 */
export const askPlannerAI = {
  optimizeSchedule: PlannerAIService.optimizeSchedule.bind(PlannerAIService),
  suggestTasks: PlannerAIService.suggestTasks.bind(PlannerAIService),
  analyzePriorities: PlannerAIService.analyzePriorities.bind(PlannerAIService),
  generateReminders: PlannerAIService.generateReminders.bind(PlannerAIService),
  assessProductivity: PlannerAIService.assessProductivity.bind(PlannerAIService),
};

/**
 * Health AI Functions
 */
export const askHealthAI = {
  analyzeSymptoms: HealthAIService.analyzeSymptoms.bind(HealthAIService),
  suggestHealthTips: HealthAIService.suggestHealthTips.bind(HealthAIService),
  interpretMetrics: HealthAIService.interpretMetrics.bind(HealthAIService),
  createMedicationPlan: HealthAIService.createMedicationPlan.bind(HealthAIService),
  assessRisk: HealthAIService.assessRisk.bind(HealthAIService),
};

/**
 * Learning AI Functions
 */
export const askLearningAI = {
  createLearningPath: LearningAIService.createLearningPath.bind(LearningAIService),
  assessSkills: LearningAIService.assessSkills.bind(LearningAIService),
  recommendContent: LearningAIService.recommendContent.bind(LearningAIService),
  generateQuiz: LearningAIService.generateQuiz.bind(LearningAIService),
  provideFeedback: LearningAIService.provideFeedback.bind(LearningAIService),
};

/**
 * Chat AI Functions
 */
export const chatAI = {
  sendMessage: ChatAIService.sendMessage.bind(ChatAIService),
  sendStreamingMessage: ChatAIService.sendStreamingMessage.bind(ChatAIService),
  continueConversation: ChatAIService.continueConversation.bind(ChatAIService),
  analyzeContext: ChatAIService.analyzeContext.bind(ChatAIService),
  switchPersonality: ChatAIService.switchPersonality.bind(ChatAIService),
};

/**
 * Service Initialization
 */
export const initializeAIServices = async (): Promise<void> => {
  try {
    await PlannerAIService.initialize();
    await HealthAIService.initialize();
    await LearningAIService.initialize();
    await ChatAIService.initialize();
    console.log('✅ AI Services initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize AI Services:', error);
    throw error;
  }
};

/**
 * Service Health Check
 */
export const checkAIServicesHealth = async (): Promise<Record<string, boolean>> => {
  return {
    planner: await PlannerAIService.healthCheck(),
    health: await HealthAIService.healthCheck(),
    learning: await LearningAIService.healthCheck(),
    chat: await ChatAIService.healthCheck(),
  };
};
