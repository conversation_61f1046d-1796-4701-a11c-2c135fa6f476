/**
 * AI Service Constants
 * 
 * ثوابت خدمات الذكاء الاصطناعي
 */

import { ProviderType, ContextType, ModelConfig } from '../types';

/**
 * Supported AI Providers
 */
export const AI_PROVIDERS: Record<ProviderType, string> = {
  openai: 'OpenAI',
  deepseek: 'DeepSeek',
  anthropic: 'Anthropic',
  local: 'Local AI',
};

/**
 * Context Types for AI Services
 */
export const CONTEXT_TYPES: Record<ContextType, string> = {
  planner: 'Smart Planner',
  health: 'Health AI',
  learning: 'Learning AI',
  chat: 'Chat AI',
  general: 'General AI',
};

/**
 * Default Model Configurations
 */
export const MODEL_CONFIGS: Record<ProviderType, Record<string, ModelConfig>> = {
  openai: {
    'gpt-4-turbo': {
      name: 'gpt-4-turbo',
      maxTokens: 4096,
      temperature: 0.7,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      streaming: true,
    },
    'gpt-4': {
      name: 'gpt-4',
      maxTokens: 8192,
      temperature: 0.7,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      streaming: true,
    },
    'gpt-3.5-turbo': {
      name: 'gpt-3.5-turbo',
      maxTokens: 4096,
      temperature: 0.7,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      streaming: true,
    },
  },
  deepseek: {
    'deepseek-chat': {
      name: 'deepseek-chat',
      maxTokens: 4096,
      temperature: 0.7,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      streaming: true,
    },
    'deepseek-coder': {
      name: 'deepseek-coder',
      maxTokens: 4096,
      temperature: 0.3,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      streaming: true,
    },
  },
  anthropic: {
    'claude-3-opus': {
      name: 'claude-3-opus-20240229',
      maxTokens: 4096,
      temperature: 0.7,
      topP: 1,
      streaming: true,
    },
    'claude-3-sonnet': {
      name: 'claude-3-sonnet-20240229',
      maxTokens: 4096,
      temperature: 0.7,
      topP: 1,
      streaming: true,
    },
  },
  local: {
    'local-model': {
      name: 'local-model',
      maxTokens: 2048,
      temperature: 0.7,
      streaming: false,
    },
  },
};

/**
 * Error Codes
 */
export const AI_ERROR_CODES = {
  // Provider Errors
  PROVIDER_ERROR: 'PROVIDER_ERROR',
  INVALID_API_KEY: 'INVALID_API_KEY',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  MODEL_NOT_FOUND: 'MODEL_NOT_FOUND',
  
  // Request Errors
  INVALID_REQUEST: 'INVALID_REQUEST',
  REQUEST_TOO_LARGE: 'REQUEST_TOO_LARGE',
  TIMEOUT: 'TIMEOUT',
  
  // Service Errors
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  ALL_PROVIDERS_FAILED: 'ALL_PROVIDERS_FAILED',
  INITIALIZATION_FAILED: 'INITIALIZATION_FAILED',
  
  // Context Errors
  INVALID_CONTEXT: 'INVALID_CONTEXT',
  TEMPLATE_NOT_FOUND: 'TEMPLATE_NOT_FOUND',
  FORMATTING_ERROR: 'FORMATTING_ERROR',
} as const;

/**
 * Default Timeouts (in milliseconds)
 */
export const TIMEOUTS = {
  REQUEST: 30000,        // 30 seconds
  STREAMING: 60000,      // 60 seconds
  HEALTH_CHECK: 10000,   // 10 seconds
  INITIALIZATION: 15000, // 15 seconds
} as const;

/**
 * Rate Limits
 */
export const RATE_LIMITS = {
  openai: {
    requestsPerMinute: 60,
    tokensPerMinute: 90000,
  },
  deepseek: {
    requestsPerMinute: 100,
    tokensPerMinute: 200000,
  },
  anthropic: {
    requestsPerMinute: 50,
    tokensPerMinute: 40000,
  },
  local: {
    requestsPerMinute: 1000,
    tokensPerMinute: 1000000,
  },
} as const;

/**
 * Cache Configuration
 */
export const CACHE_CONFIG = {
  DEFAULT_TTL: 300000,    // 5 minutes
  MAX_SIZE: 100,          // 100 cached responses
  CLEANUP_INTERVAL: 60000, // 1 minute
} as const;

/**
 * Retry Configuration
 */
export const RETRY_CONFIG = {
  MAX_ATTEMPTS: 3,
  BASE_DELAY: 1000,      // 1 second
  MAX_DELAY: 10000,      // 10 seconds
  BACKOFF_FACTOR: 2,
} as const;

/**
 * Context-Specific Settings
 */
export const CONTEXT_SETTINGS = {
  planner: {
    defaultModel: 'gpt-4-turbo',
    temperature: 0.3,      // Lower for more consistent planning
    maxTokens: 2048,
    streaming: false,      // Planning usually needs complete response
  },
  health: {
    defaultModel: 'claude-3-opus',
    temperature: 0.2,      // Very low for medical accuracy
    maxTokens: 1500,
    streaming: false,      // Health advice needs complete response
  },
  learning: {
    defaultModel: 'gpt-4',
    temperature: 0.5,      // Balanced for educational content
    maxTokens: 2048,
    streaming: true,       // Can stream educational content
  },
  chat: {
    defaultModel: 'gpt-3.5-turbo',
    temperature: 0.8,      // Higher for more conversational
    maxTokens: 1000,
    streaming: true,       // Chat benefits from streaming
  },
  general: {
    defaultModel: 'gpt-3.5-turbo',
    temperature: 0.7,      // Balanced default
    maxTokens: 1500,
    streaming: true,
  },
} as const;

/**
 * Prompt Categories
 */
export const PROMPT_CATEGORIES = {
  SYSTEM: 'system',
  TASK: 'task',
  CONTEXT: 'context',
  FORMATTING: 'formatting',
  SAFETY: 'safety',
} as const;

/**
 * Response Quality Thresholds
 */
export const QUALITY_THRESHOLDS = {
  MIN_CONFIDENCE: 0.3,
  GOOD_CONFIDENCE: 0.7,
  EXCELLENT_CONFIDENCE: 0.9,
  MIN_LENGTH: 10,
  MAX_LENGTH: 10000,
} as const;

/**
 * Feature Flags
 */
export const FEATURE_FLAGS = {
  STREAMING_ENABLED: true,
  CACHING_ENABLED: true,
  RATE_LIMITING_ENABLED: true,
  FALLBACK_PROVIDERS_ENABLED: true,
  RESPONSE_VALIDATION_ENABLED: true,
  ANALYTICS_ENABLED: true,
} as const;

/**
 * Analytics Events
 */
export const ANALYTICS_EVENTS = {
  REQUEST_SENT: 'ai_request_sent',
  REQUEST_COMPLETED: 'ai_request_completed',
  REQUEST_FAILED: 'ai_request_failed',
  PROVIDER_SWITCHED: 'ai_provider_switched',
  CACHE_HIT: 'ai_cache_hit',
  CACHE_MISS: 'ai_cache_miss',
  RATE_LIMIT_HIT: 'ai_rate_limit_hit',
  STREAMING_STARTED: 'ai_streaming_started',
  STREAMING_COMPLETED: 'ai_streaming_completed',
} as const;

/**
 * Security Settings
 */
export const SECURITY_SETTINGS = {
  API_KEY_ENCRYPTION: true,
  REQUEST_SANITIZATION: true,
  RESPONSE_FILTERING: true,
  PII_DETECTION: true,
  CONTENT_MODERATION: true,
} as const;

/**
 * Logging Levels
 */
export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
} as const;

/**
 * Health Check Intervals
 */
export const HEALTH_CHECK_INTERVALS = {
  FAST: 30000,    // 30 seconds
  NORMAL: 60000,  // 1 minute
  SLOW: 300000,   // 5 minutes
} as const;

/**
 * Default Personalities for Chat AI
 */
export const CHAT_PERSONALITIES = {
  FRIENDLY: 'friendly',
  PROFESSIONAL: 'professional',
  CASUAL: 'casual',
  ENTHUSIASTIC: 'enthusiastic',
  WISE: 'wise',
  HELPFUL: 'helpful',
} as const;

/**
 * Supported Languages
 */
export const SUPPORTED_LANGUAGES = {
  ENGLISH: 'en',
  ARABIC: 'ar',
} as const;

/**
 * Export all constants as a single object for easy access
 */
export const AI_CONSTANTS = {
  PROVIDERS: AI_PROVIDERS,
  CONTEXTS: CONTEXT_TYPES,
  MODELS: MODEL_CONFIGS,
  ERRORS: AI_ERROR_CODES,
  TIMEOUTS,
  RATE_LIMITS,
  CACHE: CACHE_CONFIG,
  RETRY: RETRY_CONFIG,
  CONTEXT_SETTINGS,
  PROMPTS: PROMPT_CATEGORIES,
  QUALITY: QUALITY_THRESHOLDS,
  FEATURES: FEATURE_FLAGS,
  ANALYTICS: ANALYTICS_EVENTS,
  SECURITY: SECURITY_SETTINGS,
  LOGGING: LOG_LEVELS,
  HEALTH: HEALTH_CHECK_INTERVALS,
  CHAT: CHAT_PERSONALITIES,
  LANGUAGES: SUPPORTED_LANGUAGES,
} as const;
