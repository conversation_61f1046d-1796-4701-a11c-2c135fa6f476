/**
 * Prompt Template Service
 * 
 * خدمة إدارة قوالب المحادثة والتوجيهات للذكاء الاصطناعي
 */

import { PromptTemplate, ContextType } from '../types';
import { Logger } from '@/core/error/Logger';

/**
 * Prompt Template Service Class
 */
export class PromptTemplateService {
  private static instance: PromptTemplateService;
  private templates: Map<string, PromptTemplate> = new Map();
  private contextTemplates: Map<ContextType, PromptTemplate[]> = new Map();

  static getInstance(): PromptTemplateService {
    if (!PromptTemplateService.instance) {
      PromptTemplateService.instance = new PromptTemplateService();
    }
    return PromptTemplateService.instance;
  }

  /**
   * Add a new prompt template
   */
  async addTemplate(template: PromptTemplate): Promise<void> {
    try {
      // Validate template
      this.validateTemplate(template);
      
      // Store template
      this.templates.set(template.id, template);
      
      // Index by context
      if (!this.contextTemplates.has(template.context)) {
        this.contextTemplates.set(template.context, []);
      }
      this.contextTemplates.get(template.context)!.push(template);
      
      Logger.debug(`✅ Added prompt template: ${template.id}`);
    } catch (error) {
      Logger.error(`❌ Failed to add prompt template ${template.id}:`, error);
      throw error;
    }
  }

  /**
   * Get a prompt template by ID
   */
  async getTemplate(id: string): Promise<PromptTemplate | null> {
    return this.templates.get(id) || null;
  }

  /**
   * Get formatted prompt with variables replaced
   */
  async getPrompt(templateId: string, variables: Record<string, string> = {}): Promise<{
    systemPrompt: string;
    userPrompt: string;
    metadata: any;
  }> {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Prompt template not found: ${templateId}`);
    }

    try {
      const systemPrompt = this.replaceVariables(template.systemPrompt, variables);
      const userPrompt = this.replaceVariables(template.userPromptTemplate, variables);
      
      return {
        systemPrompt,
        userPrompt,
        metadata: template.metadata || {},
      };
    } catch (error) {
      Logger.error(`Failed to format prompt ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Get all templates for a specific context
   */
  getTemplatesByContext(context: ContextType): PromptTemplate[] {
    return this.contextTemplates.get(context) || [];
  }

  /**
   * Get all available template IDs
   */
  getAllTemplateIds(): string[] {
    return Array.from(this.templates.keys());
  }

  /**
   * Update an existing template
   */
  async updateTemplate(id: string, updates: Partial<PromptTemplate>): Promise<void> {
    const existing = this.templates.get(id);
    if (!existing) {
      throw new Error(`Template not found: ${id}`);
    }

    const updated = { ...existing, ...updates };
    this.validateTemplate(updated);
    
    this.templates.set(id, updated);
    Logger.debug(`✅ Updated prompt template: ${id}`);
  }

  /**
   * Remove a template
   */
  async removeTemplate(id: string): Promise<boolean> {
    const template = this.templates.get(id);
    if (!template) {
      return false;
    }

    // Remove from main storage
    this.templates.delete(id);
    
    // Remove from context index
    const contextTemplates = this.contextTemplates.get(template.context);
    if (contextTemplates) {
      const index = contextTemplates.findIndex(t => t.id === id);
      if (index !== -1) {
        contextTemplates.splice(index, 1);
      }
    }

    Logger.debug(`🗑️ Removed prompt template: ${id}`);
    return true;
  }

  /**
   * Search templates by name or content
   */
  searchTemplates(query: string): PromptTemplate[] {
    const lowercaseQuery = query.toLowerCase();
    return Array.from(this.templates.values()).filter(template =>
      template.name.toLowerCase().includes(lowercaseQuery) ||
      template.systemPrompt.toLowerCase().includes(lowercaseQuery) ||
      template.userPromptTemplate.toLowerCase().includes(lowercaseQuery)
    );
  }

  /**
   * Get template statistics
   */
  getStatistics(): {
    totalTemplates: number;
    templatesByContext: Record<string, number>;
    averageVariables: number;
  } {
    const totalTemplates = this.templates.size;
    const templatesByContext: Record<string, number> = {};
    let totalVariables = 0;

    for (const [context, templates] of this.contextTemplates) {
      templatesByContext[context] = templates.length;
    }

    for (const template of this.templates.values()) {
      totalVariables += template.variables.length;
    }

    return {
      totalTemplates,
      templatesByContext,
      averageVariables: totalTemplates > 0 ? totalVariables / totalTemplates : 0,
    };
  }

  /**
   * Export templates to JSON
   */
  exportTemplates(): string {
    const templates = Array.from(this.templates.values());
    return JSON.stringify(templates, null, 2);
  }

  /**
   * Import templates from JSON
   */
  async importTemplates(jsonData: string): Promise<number> {
    try {
      const templates: PromptTemplate[] = JSON.parse(jsonData);
      let importedCount = 0;

      for (const template of templates) {
        try {
          await this.addTemplate(template);
          importedCount++;
        } catch (error) {
          Logger.warn(`Failed to import template ${template.id}:`, error);
        }
      }

      Logger.info(`📥 Imported ${importedCount} prompt templates`);
      return importedCount;
    } catch (error) {
      Logger.error('Failed to import templates:', error);
      throw error;
    }
  }

  /**
   * Clear all templates
   */
  clearAllTemplates(): void {
    this.templates.clear();
    this.contextTemplates.clear();
    Logger.info('🗑️ Cleared all prompt templates');
  }

  // Private helper methods

  private validateTemplate(template: PromptTemplate): void {
    if (!template.id || !template.name) {
      throw new Error('Template must have id and name');
    }

    if (!template.systemPrompt || !template.userPromptTemplate) {
      throw new Error('Template must have systemPrompt and userPromptTemplate');
    }

    if (!template.context) {
      throw new Error('Template must have a context');
    }

    if (!Array.isArray(template.variables)) {
      throw new Error('Template variables must be an array');
    }

    // Check if all variables in templates are declared
    const systemVariables = this.extractVariables(template.systemPrompt);
    const userVariables = this.extractVariables(template.userPromptTemplate);
    const allVariables = [...systemVariables, ...userVariables];
    
    for (const variable of allVariables) {
      if (!template.variables.includes(variable)) {
        Logger.warn(`Variable ${variable} used in template but not declared in variables array`);
      }
    }
  }

  private replaceVariables(text: string, variables: Record<string, string>): string {
    let result = text;
    
    // Replace variables in format {variableName}
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`\\{${key}\\}`, 'g');
      result = result.replace(regex, value);
    }

    // Check for unreplaced variables
    const unreplacedVariables = this.extractVariables(result);
    if (unreplacedVariables.length > 0) {
      Logger.warn(`Unreplaced variables found: ${unreplacedVariables.join(', ')}`);
    }

    return result;
  }

  private extractVariables(text: string): string[] {
    const regex = /\{([^}]+)\}/g;
    const variables: string[] = [];
    let match;

    while ((match = regex.exec(text)) !== null) {
      variables.push(match[1]);
    }

    return [...new Set(variables)]; // Remove duplicates
  }
}
