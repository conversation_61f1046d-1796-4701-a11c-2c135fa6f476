/**
 * LifeAI Assistant - Main Entry Point
 * 
 * This file serves as the main entry point for the LifeAI Assistant application.
 * It exports all the core modules and provides a centralized access point
 * for the entire application architecture.
 */

// Core exports
export * from './core';

// Feature exports
export * from './features';

// Shared utilities and components
export * from './shared';

// Services
export * from './services';

// UI system
export * from './ui';

// AI modules
export * from './ai';

// Localization
export * from './locales';

// App configuration and constants
export { default as AppConfig } from './core/constants/config';
export { default as AppTheme } from './ui/theme';

/**
 * Application version and metadata
 */
export const APP_VERSION = '1.0.0';
export const APP_NAME = 'LifeAI Assistant';
export const APP_DESCRIPTION = 'AI-powered mobile app for life management';
