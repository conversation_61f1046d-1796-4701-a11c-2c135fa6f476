/**
 * Core AI Service
 * 
 * الخدمة الأساسية للذكاء الاصطناعي مع دعم متعدد المقدمين
 */

import { 
  AIProvider, 
  AIRequest, 
  AIResponse, 
  StreamingResponse, 
  ProviderType, 
  AIError,
  ContextType 
} from '../types';
import { AIConfig } from '../config/AIConfig';
import { AIServiceFactory } from './AIServiceFactory';
import { Logger } from '@/core/error/Logger';
import { CacheManager } from '../utils/CacheManager';
import { RateLimiter } from '../utils/RateLimiter';

/**
 * Main AI Service Class
 */
export class AIService {
  private static instance: AIService;
  private providers: Map<ProviderType, AIProvider> = new Map();
  private currentProvider: ProviderType;
  private fallbackProviders: ProviderType[];
  private cacheManager: CacheManager;
  private rateLimiter: RateLimiter;
  private initialized = false;

  private constructor() {
    const config = AIConfig.getServiceConfig();
    this.currentProvider = config.defaultProvider;
    this.fallbackProviders = config.fallbackProviders;
    this.cacheManager = new CacheManager(config.caching);
    this.rateLimiter = new RateLimiter(config.rateLimiting);
  }

  /**
   * Get singleton instance
   */
  static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  /**
   * Initialize AI service
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await AIConfig.initialize();
      
      // Initialize available providers
      const availableProviders = AIConfig.getAvailableProviders();
      
      for (const providerType of availableProviders) {
        try {
          const provider = await AIServiceFactory.createProvider(providerType);
          if (await provider.healthCheck()) {
            this.providers.set(providerType, provider);
            Logger.info(`✅ ${providerType} provider initialized`);
          } else {
            Logger.warn(`⚠️ ${providerType} provider health check failed`);
          }
        } catch (error) {
          Logger.error(`❌ Failed to initialize ${providerType} provider:`, error);
        }
      }

      if (this.providers.size === 0) {
        throw new Error('No AI providers available');
      }

      // Set current provider to first available if current is not available
      if (!this.providers.has(this.currentProvider)) {
        this.currentProvider = Array.from(this.providers.keys())[0];
        Logger.info(`🔄 Switched to ${this.currentProvider} as primary provider`);
      }

      this.initialized = true;
      Logger.info('🚀 AI Service initialized successfully');
    } catch (error) {
      Logger.error('❌ Failed to initialize AI Service:', error);
      throw error;
    }
  }

  /**
   * Send AI request with automatic fallback
   */
  async sendRequest(request: AIRequest): Promise<AIResponse> {
    if (!this.initialized) {
      await this.initialize();
    }

    // Check rate limiting
    if (!this.rateLimiter.canMakeRequest()) {
      throw this.createAIError('RATE_LIMIT_EXCEEDED', 'Rate limit exceeded', this.currentProvider);
    }

    // Check cache first
    const cacheKey = this.generateCacheKey(request);
    const cachedResponse = this.cacheManager.get(cacheKey);
    if (cachedResponse) {
      Logger.debug('📦 Returning cached response');
      return cachedResponse;
    }

    const providers = [this.currentProvider, ...this.fallbackProviders];
    let lastError: AIError | null = null;

    for (const providerType of providers) {
      const provider = this.providers.get(providerType);
      if (!provider) continue;

      try {
        Logger.debug(`🤖 Sending request to ${providerType}`);
        
        // Add model selection based on context
        const enhancedRequest = this.enhanceRequest(request, providerType);
        
        const response = await provider.sendRequest(enhancedRequest);
        
        // Cache successful response
        this.cacheManager.set(cacheKey, response);
        
        // Update rate limiter
        this.rateLimiter.recordRequest();
        
        Logger.debug(`✅ Request successful with ${providerType}`);
        return response;
        
      } catch (error) {
        lastError = this.handleProviderError(error, providerType);
        Logger.warn(`⚠️ ${providerType} failed:`, lastError.message);
        
        // If this is the current provider and it failed, try to switch
        if (providerType === this.currentProvider) {
          await this.handleProviderFailure(providerType);
        }
      }
    }

    // All providers failed
    throw lastError || this.createAIError('ALL_PROVIDERS_FAILED', 'All AI providers failed', this.currentProvider);
  }

  /**
   * Send streaming AI request
   */
  async* sendStreamingRequest(request: AIRequest): AsyncGenerator<StreamingResponse, void, unknown> {
    if (!this.initialized) {
      await this.initialize();
    }

    // Check rate limiting
    if (!this.rateLimiter.canMakeRequest()) {
      throw this.createAIError('RATE_LIMIT_EXCEEDED', 'Rate limit exceeded', this.currentProvider);
    }

    const providers = [this.currentProvider, ...this.fallbackProviders];
    let lastError: AIError | null = null;

    for (const providerType of providers) {
      const provider = this.providers.get(providerType);
      if (!provider) continue;

      try {
        Logger.debug(`🌊 Starting streaming request to ${providerType}`);
        
        // Add model selection based on context
        const enhancedRequest = this.enhanceRequest(request, providerType);
        enhancedRequest.stream = true;
        
        const streamGenerator = provider.sendStreamingRequest(enhancedRequest);
        
        // Update rate limiter
        this.rateLimiter.recordRequest();
        
        yield* streamGenerator;
        
        Logger.debug(`✅ Streaming request successful with ${providerType}`);
        return;
        
      } catch (error) {
        lastError = this.handleProviderError(error, providerType);
        Logger.warn(`⚠️ ${providerType} streaming failed:`, lastError.message);
        
        // If this is the current provider and it failed, try to switch
        if (providerType === this.currentProvider) {
          await this.handleProviderFailure(providerType);
        }
      }
    }

    // All providers failed
    throw lastError || this.createAIError('ALL_PROVIDERS_FAILED', 'All AI providers failed for streaming', this.currentProvider);
  }

  /**
   * Get current provider status
   */
  getCurrentProvider(): ProviderType {
    return this.currentProvider;
  }

  /**
   * Switch to different provider
   */
  async switchProvider(providerType: ProviderType): Promise<void> {
    if (!this.providers.has(providerType)) {
      throw new Error(`Provider ${providerType} is not available`);
    }

    const provider = this.providers.get(providerType)!;
    if (!(await provider.healthCheck())) {
      throw new Error(`Provider ${providerType} is not healthy`);
    }

    this.currentProvider = providerType;
    Logger.info(`🔄 Switched to ${providerType} provider`);
  }

  /**
   * Get available providers
   */
  getAvailableProviders(): ProviderType[] {
    return Array.from(this.providers.keys());
  }

  /**
   * Health check for all providers
   */
  async healthCheck(): Promise<Record<ProviderType, boolean>> {
    const results: Record<string, boolean> = {};
    
    for (const [providerType, provider] of this.providers) {
      try {
        results[providerType] = await provider.healthCheck();
      } catch (error) {
        results[providerType] = false;
      }
    }
    
    return results as Record<ProviderType, boolean>;
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cacheManager.clear();
    Logger.info('🗑️ AI Service cache cleared');
  }

  /**
   * Get service statistics
   */
  getStatistics() {
    return {
      currentProvider: this.currentProvider,
      availableProviders: this.getAvailableProviders(),
      cacheStats: this.cacheManager.getStats(),
      rateLimitStats: this.rateLimiter.getStats(),
    };
  }

  // Private helper methods

  private enhanceRequest(request: AIRequest, providerType: ProviderType): AIRequest {
    const context = request.context || 'general';
    const model = AIConfig.getModelForContext(context, providerType);
    const modelConfig = AIConfig.getModelConfig(providerType, model);

    return {
      ...request,
      model: request.model || model,
      maxTokens: request.maxTokens || modelConfig?.maxTokens,
      temperature: request.temperature ?? modelConfig?.temperature,
    };
  }

  private generateCacheKey(request: AIRequest): string {
    const key = {
      messages: request.messages,
      model: request.model,
      temperature: request.temperature,
      context: request.context,
    };
    return JSON.stringify(key);
  }

  private handleProviderError(error: any, providerType: ProviderType): AIError {
    if (error instanceof Error && 'code' in error) {
      return error as AIError;
    }

    return this.createAIError(
      'PROVIDER_ERROR',
      error.message || 'Unknown provider error',
      providerType,
      error.status || 500
    );
  }

  private createAIError(code: string, message: string, provider: ProviderType, statusCode?: number): AIError {
    const error = new Error(message) as AIError;
    error.code = code;
    error.provider = provider;
    error.statusCode = statusCode;
    error.retryable = statusCode ? statusCode >= 500 : false;
    return error;
  }

  private async handleProviderFailure(providerType: ProviderType): Promise<void> {
    // Try to switch to a healthy fallback provider
    for (const fallbackProvider of this.fallbackProviders) {
      if (fallbackProvider === providerType) continue;
      
      const provider = this.providers.get(fallbackProvider);
      if (provider && await provider.healthCheck()) {
        this.currentProvider = fallbackProvider;
        Logger.info(`🔄 Auto-switched to ${fallbackProvider} due to ${providerType} failure`);
        return;
      }
    }
    
    Logger.warn(`⚠️ No healthy fallback provider available after ${providerType} failure`);
  }
}
