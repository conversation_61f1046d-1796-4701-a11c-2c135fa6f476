/**
 * Learning Dashboard Screen
 * 
 * لوحة تحكم التعلم الذاتي مع الذكاء الاصطناعي
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';

// Shared components
import { Container, Card, Button, Loading } from '@/shared/components';
import { useTheme } from '@/ui/theme/useTheme';

// Feature components
import CourseCard from '../components/CourseCard';
import SkillProgressChart from '../components/SkillProgressChart';
import LearningPathVisualizer from '../components/LearningPathVisualizer';
import StudyTimer from '../components/StudyTimer';
import AITutor from '../components/AITutor';

// Hooks
import { useLearningProgress } from '../hooks/useLearningProgress';
import { useCourseRecommendations } from '../hooks/useCourseRecommendations';
import { useLearningAnalytics } from '../hooks/useLearningAnalytics';

// Types
import type { Course, LearningRecommendation, StudySession } from '../../domain/entities';

/**
 * Learning Dashboard Screen Component
 */
const LearningDashboardScreen: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigation = useNavigation();

  // State
  const [refreshing, setRefreshing] = useState(false);
  const [activeStudySession, setActiveStudySession] = useState<StudySession | null>(null);

  // Hooks
  const {
    currentCourses,
    completedCourses,
    learningGoals,
    overallProgress,
    isLoading: progressLoading,
    refreshProgress,
  } = useLearningProgress();

  const {
    recommendedCourses,
    personalizedPath,
    skillGaps,
    isLoading: recommendationsLoading,
    refreshRecommendations,
  } = useCourseRecommendations();

  const {
    weeklyStats,
    learningStreak,
    timeSpentToday,
    skillsImproved,
    isLoading: analyticsLoading,
    refreshAnalytics,
  } = useLearningAnalytics();

  // Effects
  useEffect(() => {
    refreshProgress();
    refreshRecommendations();
    refreshAnalytics();
  }, []);

  // Handlers
  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      refreshProgress(),
      refreshRecommendations(),
      refreshAnalytics(),
    ]);
    setRefreshing(false);
  };

  const handleCoursePress = (course: Course) => {
    navigation.navigate('CourseDetails', { courseId: course.id });
  };

  const handleStartStudySession = () => {
    navigation.navigate('StudySessionScreen');
  };

  const handleSkillAssessment = () => {
    navigation.navigate('SkillAssessmentScreen');
  };

  const handleViewLearningPath = () => {
    navigation.navigate('LearningPathScreen');
  };

  const handleCourseLibrary = () => {
    navigation.navigate('CourseLibraryScreen');
  };

  const handleRecommendationAction = (recommendation: LearningRecommendation) => {
    // Handle AI learning recommendation actions
    console.log('Applying learning recommendation:', recommendation);
  };

  // Loading state
  if (progressLoading || recommendationsLoading || analyticsLoading) {
    return (
      <Container style={styles.container}>
        <Loading />
      </Container>
    );
  }

  return (
    <Container style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            {t('learningAI.dashboard.title')}
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            {t('learningAI.dashboard.subtitle')}
          </Text>
        </View>

        {/* Learning Stats */}
        <Card style={styles.statsCard}>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.primary.main }]}>
                {learningStreak}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                {t('learningAI.stats.streak')}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.success.main }]}>
                {timeSpentToday}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                {t('learningAI.stats.todayMinutes')}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.warning.main }]}>
                {skillsImproved}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                {t('learningAI.stats.skillsImproved')}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.info.main }]}>
                {Math.round(overallProgress * 100)}%
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                {t('learningAI.stats.progress')}
              </Text>
            </View>
          </View>
        </Card>

        {/* AI Tutor */}
        <AITutor
          onRecommendationAction={handleRecommendationAction}
          skillGaps={skillGaps}
        />

        {/* Study Session */}
        <Card style={styles.studyCard}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('learningAI.study.session')}
            </Text>
            {activeStudySession && (
              <Text style={[styles.activeSession, { color: theme.colors.success.main }]}>
                {t('learningAI.study.active')}
              </Text>
            )}
          </View>
          <StudyTimer
            activeSession={activeStudySession}
            onSessionStart={setActiveStudySession}
            onSessionEnd={() => setActiveStudySession(null)}
          />
          <Button
            title={t('learningAI.study.startSession')}
            onPress={handleStartStudySession}
            style={styles.studyButton}
          />
        </Card>

        {/* Current Courses */}
        <Card style={styles.coursesCard}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('learningAI.courses.current')}
            </Text>
            <TouchableOpacity onPress={handleCourseLibrary}>
              <Text style={[styles.viewAllText, { color: theme.colors.primary.main }]}>
                {t('common.viewAll')}
              </Text>
            </TouchableOpacity>
          </View>
          {currentCourses.length > 0 ? (
            currentCourses.slice(0, 3).map((course) => (
              <CourseCard
                key={course.id}
                course={course}
                onPress={() => handleCoursePress(course)}
                compact={true}
              />
            ))
          ) : (
            <View style={styles.emptyState}>
              <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                {t('learningAI.courses.noCurrent')}
              </Text>
              <Button
                title={t('learningAI.courses.browse')}
                onPress={handleCourseLibrary}
                variant="outline"
                style={styles.browseButton}
              />
            </View>
          )}
        </Card>

        {/* Skills Progress */}
        <Card style={styles.skillsCard}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('learningAI.skills.progress')}
            </Text>
            <TouchableOpacity onPress={handleSkillAssessment}>
              <Text style={[styles.viewAllText, { color: theme.colors.primary.main }]}>
                {t('learningAI.skills.assess')}
              </Text>
            </TouchableOpacity>
          </View>
          <SkillProgressChart
            weeklyStats={weeklyStats}
            compact={true}
          />
        </Card>

        {/* Learning Path */}
        <Card style={styles.pathCard}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('learningAI.path.personalized')}
            </Text>
            <TouchableOpacity onPress={handleViewLearningPath}>
              <Text style={[styles.viewAllText, { color: theme.colors.primary.main }]}>
                {t('common.viewDetails')}
              </Text>
            </TouchableOpacity>
          </View>
          <LearningPathVisualizer
            learningPath={personalizedPath}
            compact={true}
          />
        </Card>

        {/* Recommended Courses */}
        {recommendedCourses.length > 0 && (
          <Card style={styles.recommendationsCard}>
            <View style={styles.cardHeader}>
              <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
                {t('learningAI.recommendations.title')}
              </Text>
            </View>
            {recommendedCourses.slice(0, 2).map((course) => (
              <CourseCard
                key={course.id}
                course={course}
                onPress={() => handleCoursePress(course)}
                recommended={true}
                compact={true}
              />
            ))}
          </Card>
        )}

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <Button
            title={t('learningAI.actions.startLearning')}
            onPress={handleStartStudySession}
            style={styles.actionButton}
            icon="play"
          />
          <Button
            title={t('learningAI.actions.skillTest')}
            onPress={handleSkillAssessment}
            variant="outline"
            style={styles.actionButton}
            icon="brain"
          />
        </View>

        {/* Learning Goals */}
        {learningGoals.length > 0 && (
          <Card style={styles.goalsCard}>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              {t('learningAI.goals.current')}
            </Text>
            {learningGoals.slice(0, 3).map((goal, index) => (
              <View key={index} style={styles.goalItem}>
                <Text style={[styles.goalTitle, { color: theme.colors.text }]}>
                  {goal.title}
                </Text>
                <View style={styles.goalProgress}>
                  <View 
                    style={[
                      styles.goalProgressBar, 
                      { 
                        width: `${goal.progress * 100}%`,
                        backgroundColor: theme.colors.primary.main 
                      }
                    ]} 
                  />
                </View>
                <Text style={[styles.goalPercentage, { color: theme.colors.textSecondary }]}>
                  {Math.round(goal.progress * 100)}%
                </Text>
              </View>
            ))}
          </Card>
        )}
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  statsCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  studyCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  coursesCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  skillsCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  pathCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  recommendationsCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  goalsCard: {
    margin: 20,
    marginTop: 10,
    padding: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
  },
  activeSession: {
    fontSize: 12,
    fontWeight: '500',
  },
  studyButton: {
    marginTop: 12,
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 12,
  },
  browseButton: {
    minWidth: 120,
  },
  quickActions: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  goalItem: {
    marginBottom: 16,
  },
  goalTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  goalProgress: {
    height: 6,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 3,
    marginBottom: 4,
  },
  goalProgressBar: {
    height: '100%',
    borderRadius: 3,
  },
  goalPercentage: {
    fontSize: 12,
    textAlign: 'right',
  },
});

export default LearningDashboardScreen;
