/**
 * Smart Shopping Feature Module
 * 
 * مساعد تسوق ذكي مدعوم بالذكاء الاصطناعي
 * - قوائم تسوق ذكية
 * - مقارنة الأسعار
 * - توصيات المنتجات
 * - تتبع الميزانية
 * - تحليل عادات الشراء
 */

// Domain Layer Exports
export * from './domain/entities';
export * from './domain/usecases';
export type { 
  ShoppingRepository, 
  ProductRepository, 
  BudgetRepository,
  WishlistRepository 
} from './domain/repositories';

// Data Layer Exports
export { ShoppingRepositoryImpl } from './data/repositories/ShoppingRepositoryImpl';
export { ProductRepositoryImpl } from './data/repositories/ProductRepositoryImpl';
export { BudgetRepositoryImpl } from './data/repositories/BudgetRepositoryImpl';

// Presentation Layer Exports
export { default as ShoppingDashboardScreen } from './presentation/screens/ShoppingDashboardScreen';
export { default as ShoppingListScreen } from './presentation/screens/ShoppingListScreen';
export { default as ProductSearchScreen } from './presentation/screens/ProductSearchScreen';
export { default as BudgetTrackingScreen } from './presentation/screens/BudgetTrackingScreen';
export { default as DealsAndOffersScreen } from './presentation/screens/DealsAndOffersScreen';

// Components
export { default as ShoppingListCard } from './presentation/components/ShoppingListCard';
export { default as ProductCard } from './presentation/components/ProductCard';
export { default as PriceComparisonChart } from './presentation/components/PriceComparisonChart';
export { default as BudgetOverview } from './presentation/components/BudgetOverview';
export { default as SmartRecommendations } from './presentation/components/SmartRecommendations';
export { default as BarcodeScanner } from './presentation/components/BarcodeScanner';

// Hooks
export { useShoppingLists } from './presentation/hooks/useShoppingLists';
export { useProductSearch } from './presentation/hooks/useProductSearch';
export { usePriceTracking } from './presentation/hooks/usePriceTracking';
export { useBudgetTracking } from './presentation/hooks/useBudgetTracking';
export { useShoppingRecommendations } from './presentation/hooks/useShoppingRecommendations';

// Store
export { smartShoppingSlice, smartShoppingActions } from './presentation/store/smartShoppingSlice';
export type { SmartShoppingState } from './presentation/store/smartShoppingSlice';

// Infrastructure
export { AIShoppingAssistantService } from './infrastructure/services/AIShoppingAssistantService';
export { PriceComparisonService } from './infrastructure/services/PriceComparisonService';
export { BudgetAnalyticsService } from './infrastructure/services/BudgetAnalyticsService';

// Types
export type {
  ShoppingList,
  ShoppingItem,
  Product,
  ProductCategory,
  PriceHistory,
  Budget,
  Deal,
  ShoppingRecommendation,
  PurchaseHistory,
  ShoppingAnalytics,
  Wishlist,
} from './domain/entities';

// Constants
export { SMART_SHOPPING_ROUTES } from './infrastructure/config/routes';
export { PRODUCT_CATEGORIES, BUDGET_CATEGORIES } from './infrastructure/config/constants';

/**
 * Feature Module Configuration
 */
export const SmartShoppingModule = {
  name: 'SmartShopping',
  version: '1.0.0',
  description: 'AI-powered smart shopping assistant',
  dependencies: ['ai', 'camera', 'location', 'notifications'],
  permissions: ['camera', 'location'],
  routes: [
    'ShoppingDashboardScreen',
    'ShoppingListScreen',
    'ProductSearchScreen',
    'BudgetTrackingScreen',
    'DealsAndOffersScreen',
  ],
} as const;
