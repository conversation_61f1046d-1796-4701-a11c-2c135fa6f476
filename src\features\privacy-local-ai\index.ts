/**
 * Privacy & Local AI Feature Module
 * 
 * إعدادات الخصوصية والحماية مع الذكاء الاصطناعي المحلي
 * - معالجة البيانات محلياً
 * - تشفير متقدم
 * - إدارة الأذونات
 * - تدقيق الخصوصية
 * - AI محلي بدون إنترنت
 */

// Domain Layer Exports
export * from './domain/entities';
export * from './domain/usecases';
export type { 
  PrivacyRepository, 
  EncryptionRepository, 
  LocalAIRepository,
  AuditRepository 
} from './domain/repositories';

// Data Layer Exports
export { PrivacyRepositoryImpl } from './data/repositories/PrivacyRepositoryImpl';
export { EncryptionRepositoryImpl } from './data/repositories/EncryptionRepositoryImpl';
export { LocalAIRepositoryImpl } from './data/repositories/LocalAIRepositoryImpl';

// Presentation Layer Exports
export { default as PrivacyDashboardScreen } from './presentation/screens/PrivacyDashboardScreen';
export { default as DataManagementScreen } from './presentation/screens/DataManagementScreen';
export { default as EncryptionSettingsScreen } from './presentation/screens/EncryptionSettingsScreen';
export { default as LocalAISettingsScreen } from './presentation/screens/LocalAISettingsScreen';
export { default as PrivacyAuditScreen } from './presentation/screens/PrivacyAuditScreen';

// Components
export { default as PrivacyScoreCard } from './presentation/components/PrivacyScoreCard';
export { default as DataUsageChart } from './presentation/components/DataUsageChart';
export { default as PermissionManager } from './presentation/components/PermissionManager';
export { default as EncryptionStatus } from './presentation/components/EncryptionStatus';
export { default as LocalAIStatus } from './presentation/components/LocalAIStatus';
export { default as PrivacyAuditReport } from './presentation/components/PrivacyAuditReport';

// Hooks
export { usePrivacySettings } from './presentation/hooks/usePrivacySettings';
export { useDataManagement } from './presentation/hooks/useDataManagement';
export { useEncryption } from './presentation/hooks/useEncryption';
export { useLocalAI } from './presentation/hooks/useLocalAI';
export { usePrivacyAudit } from './presentation/hooks/usePrivacyAudit';

// Store
export { privacyLocalAISlice, privacyLocalAIActions } from './presentation/store/privacyLocalAISlice';
export type { PrivacyLocalAIState } from './presentation/store/privacyLocalAISlice';

// Infrastructure
export { LocalAIEngineService } from './infrastructure/services/LocalAIEngineService';
export { AdvancedEncryptionService } from './infrastructure/services/AdvancedEncryptionService';
export { PrivacyAuditService } from './infrastructure/services/PrivacyAuditService';
export { DataAnonymizationService } from './infrastructure/services/DataAnonymizationService';

// Types
export type {
  PrivacySettings,
  DataCategory,
  EncryptionConfig,
  LocalAIModel,
  PrivacyAudit,
  DataUsageMetrics,
  PermissionStatus,
  SecurityLevel,
  DataRetentionPolicy,
  AnonymizationConfig,
} from './domain/entities';

// Constants
export { PRIVACY_LOCAL_AI_ROUTES } from './infrastructure/config/routes';
export { SECURITY_LEVELS, DATA_CATEGORIES } from './infrastructure/config/constants';

/**
 * Feature Module Configuration
 */
export const PrivacyLocalAIModule = {
  name: 'PrivacyLocalAI',
  version: '1.0.0',
  description: 'Privacy-focused local AI processing',
  dependencies: ['encryption', 'storage', 'analytics'],
  permissions: ['storage'],
  routes: [
    'PrivacyDashboardScreen',
    'DataManagementScreen',
    'EncryptionSettingsScreen',
    'LocalAISettingsScreen',
    'PrivacyAuditScreen',
  ],
} as const;
