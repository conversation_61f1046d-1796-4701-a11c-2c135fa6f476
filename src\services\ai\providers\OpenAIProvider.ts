/**
 * OpenAI Provider Implementation
 * 
 * تنفيذ مقدم خدمة OpenAI مع دعم GPT-4 والـ streaming
 */

import { 
  AIProvider, 
  AIRequest, 
  AIResponse, 
  StreamingResponse, 
  ProviderType,
  AIError,
  ModelConfig 
} from '../types';
import { MODEL_CONFIGS } from '../config/AIConfig';
import { Logger } from '@/core/error/Logger';

interface OpenAIConfig {
  apiKey: string;
  baseURL?: string;
  organization?: string;
  timeout?: number;
}

interface OpenAIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  name?: string;
}

interface OpenAIRequestBody {
  model: string;
  messages: OpenAIMessage[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string[];
  stream?: boolean;
  user?: string;
}

interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message?: {
      role: string;
      content: string;
    };
    delta?: {
      role?: string;
      content?: string;
    };
    finish_reason: string | null;
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * OpenAI Provider Class
 */
export class OpenAIProvider implements AIProvider {
  name: ProviderType = 'openai';
  apiKey: string;
  baseURL: string;
  organization?: string;
  timeout: number;
  models: Record<string, ModelConfig>;
  rateLimit = {
    requestsPerMinute: 60,
    tokensPerMinute: 90000,
  };

  private requestCount = 0;
  private lastResetTime = Date.now();

  constructor(config: OpenAIConfig) {
    this.apiKey = config.apiKey;
    this.baseURL = config.baseURL || 'https://api.openai.com/v1';
    this.organization = config.organization;
    this.timeout = config.timeout || 30000;
    this.models = MODEL_CONFIGS.openai;
  }

  /**
   * Send AI request to OpenAI
   */
  async sendRequest(request: AIRequest): Promise<AIResponse> {
    try {
      const requestBody = this.buildRequestBody(request);
      const response = await this.makeAPICall('/chat/completions', requestBody);
      
      return this.parseResponse(response, request);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Send streaming AI request to OpenAI
   */
  async* sendStreamingRequest(request: AIRequest): AsyncGenerator<StreamingResponse, void, unknown> {
    try {
      const requestBody = this.buildRequestBody(request, true);
      const response = await this.makeStreamingAPICall('/chat/completions', requestBody);
      
      if (!response.body) {
        throw new Error('No response body for streaming');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let responseId = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) break;
          
          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() === '') continue;
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              
              if (data === '[DONE]') {
                return;
              }

              try {
                const parsed: OpenAIResponse = JSON.parse(data);
                responseId = parsed.id;
                
                const choice = parsed.choices[0];
                if (choice?.delta?.content) {
                  yield {
                    id: responseId,
                    delta: choice.delta.content,
                    isComplete: false,
                  };
                }

                if (choice?.finish_reason) {
                  yield {
                    id: responseId,
                    delta: '',
                    isComplete: true,
                    usage: parsed.usage,
                  };
                  return;
                }
              } catch (parseError) {
                Logger.warn('Failed to parse streaming response:', parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Health check for OpenAI service
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.makeAPICall('/models', null, 'GET');
      return response.data && Array.isArray(response.data);
    } catch (error) {
      Logger.warn('OpenAI health check failed:', error);
      return false;
    }
  }

  /**
   * Validate API key format
   */
  async validateApiKey(): Promise<boolean> {
    if (!this.apiKey || !this.apiKey.startsWith('sk-')) {
      return false;
    }

    try {
      return await this.healthCheck();
    } catch {
      return false;
    }
  }

  /**
   * Get available models
   */
  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.makeAPICall('/models', null, 'GET');
      return response.data
        .filter((model: any) => model.id.includes('gpt'))
        .map((model: any) => model.id);
    } catch (error) {
      Logger.warn('Failed to get available models:', error);
      return Object.keys(this.models);
    }
  }

  /**
   * Estimate tokens for text
   */
  estimateTokens(text: string): number {
    // Rough estimation: ~4 characters per token for English
    // More accurate estimation would require tiktoken library
    return Math.ceil(text.length / 4);
  }

  // Private helper methods

  private buildRequestBody(request: AIRequest, stream = false): OpenAIRequestBody {
    const model = request.model || 'gpt-3.5-turbo';
    const modelConfig = this.models[model] || this.models['gpt-3.5-turbo'];

    return {
      model,
      messages: request.messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        name: msg.name,
      })),
      max_tokens: request.maxTokens || modelConfig.maxTokens,
      temperature: request.temperature ?? modelConfig.temperature,
      top_p: modelConfig.topP,
      frequency_penalty: modelConfig.frequencyPenalty,
      presence_penalty: modelConfig.presencePenalty,
      stop: modelConfig.stopSequences,
      stream,
      user: request.userId,
    };
  }

  private async makeAPICall(
    endpoint: string, 
    body: any, 
    method: 'GET' | 'POST' = 'POST'
  ): Promise<any> {
    const url = `${this.baseURL}${endpoint}`;
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
    };

    if (this.organization) {
      headers['OpenAI-Organization'] = this.organization;
    }

    const config: RequestInit = {
      method,
      headers,
      signal: AbortSignal.timeout(this.timeout),
    };

    if (body && method === 'POST') {
      config.body = JSON.stringify(body);
    }

    const response = await fetch(url, config);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`OpenAI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    return response.json();
  }

  private async makeStreamingAPICall(endpoint: string, body: any): Promise<Response> {
    const url = `${this.baseURL}${endpoint}`;
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
    };

    if (this.organization) {
      headers['OpenAI-Organization'] = this.organization;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
      signal: AbortSignal.timeout(this.timeout),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`OpenAI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    return response;
  }

  private parseResponse(response: OpenAIResponse, request: AIRequest): AIResponse {
    const choice = response.choices[0];
    if (!choice?.message) {
      throw new Error('Invalid response format from OpenAI');
    }

    return {
      id: response.id,
      content: choice.message.content,
      model: response.model,
      usage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0,
      },
      finishReason: choice.finish_reason as any || 'stop',
      timestamp: new Date(),
      metadata: {
        provider: 'openai',
        requestId: request.sessionId,
      },
    };
  }

  private handleError(error: any): AIError {
    const aiError = new Error(error.message || 'OpenAI provider error') as AIError;
    aiError.code = error.code || 'OPENAI_ERROR';
    aiError.provider = 'openai';
    aiError.retryable = error.status >= 500 || error.code === 'ECONNRESET';
    aiError.statusCode = error.status;
    aiError.details = error;
    
    return aiError;
  }
}
