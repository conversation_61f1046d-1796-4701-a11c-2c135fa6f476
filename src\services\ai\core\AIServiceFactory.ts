/**
 * AI Service Factory
 * 
 * مصنع لإنشاء مقدمي خدمات الذكاء الاصطناعي
 */

import { AIProvider, ProviderType } from '../types';
import { AIConfig } from '../config/AIConfig';
import { OpenAIProvider } from '../providers/OpenAIProvider';
import { DeepSeekProvider } from '../providers/DeepSeekProvider';
import { AnthropicProvider } from '../providers/AnthropicProvider';
import { LocalAIProvider } from '../providers/LocalAIProvider';

/**
 * AI Service Factory Class
 */
export class AIServiceFactory {
  private static providerInstances: Map<ProviderType, AIProvider> = new Map();

  /**
   * Create or get provider instance
   */
  static async createProvider(providerType: ProviderType): Promise<AIProvider> {
    // Return existing instance if available
    if (this.providerInstances.has(providerType)) {
      return this.providerInstances.get(providerType)!;
    }

    // Get API key for provider
    const apiKey = AIConfig.getAPIKey(providerType);
    if (!apiKey && providerType !== 'local') {
      throw new Error(`API key not found for provider: ${providerType}`);
    }

    // Create new provider instance
    let provider: AIProvider;

    switch (providerType) {
      case 'openai':
        provider = new OpenAIProvider({
          apiKey: apiKey!,
          baseURL: AIConfig.getProviderURL(providerType),
        });
        break;

      case 'deepseek':
        provider = new DeepSeekProvider({
          apiKey: apiKey!,
          baseURL: AIConfig.getProviderURL(providerType),
        });
        break;

      case 'anthropic':
        provider = new AnthropicProvider({
          apiKey: apiKey!,
          baseURL: AIConfig.getProviderURL(providerType),
        });
        break;

      case 'local':
        provider = new LocalAIProvider({
          baseURL: AIConfig.getProviderURL(providerType),
        });
        break;

      default:
        throw new Error(`Unsupported provider type: ${providerType}`);
    }

    // Validate provider
    await this.validateProvider(provider);

    // Cache instance
    this.providerInstances.set(providerType, provider);

    return provider;
  }

  /**
   * Get all available provider types
   */
  static getAvailableProviderTypes(): ProviderType[] {
    return ['openai', 'deepseek', 'anthropic', 'local'];
  }

  /**
   * Check if provider type is supported
   */
  static isProviderSupported(providerType: string): providerType is ProviderType {
    return this.getAvailableProviderTypes().includes(providerType as ProviderType);
  }

  /**
   * Create multiple providers
   */
  static async createProviders(providerTypes: ProviderType[]): Promise<Map<ProviderType, AIProvider>> {
    const providers = new Map<ProviderType, AIProvider>();
    const errors: Array<{ provider: ProviderType; error: Error }> = [];

    for (const providerType of providerTypes) {
      try {
        const provider = await this.createProvider(providerType);
        providers.set(providerType, provider);
      } catch (error) {
        errors.push({ provider: providerType, error: error as Error });
      }
    }

    if (providers.size === 0 && errors.length > 0) {
      throw new Error(`Failed to create any providers: ${errors.map(e => `${e.provider}: ${e.error.message}`).join(', ')}`);
    }

    return providers;
  }

  /**
   * Validate provider configuration and connectivity
   */
  private static async validateProvider(provider: AIProvider): Promise<void> {
    try {
      // Validate API key format
      if (!await provider.validateApiKey()) {
        throw new Error('Invalid API key format');
      }

      // Test connectivity
      if (!await provider.healthCheck()) {
        throw new Error('Provider health check failed');
      }

    } catch (error) {
      throw new Error(`Provider validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Clear provider instances (useful for testing or reconfiguration)
   */
  static clearInstances(): void {
    this.providerInstances.clear();
  }

  /**
   * Get provider instance if exists
   */
  static getProviderInstance(providerType: ProviderType): AIProvider | null {
    return this.providerInstances.get(providerType) || null;
  }

  /**
   * Remove specific provider instance
   */
  static removeProviderInstance(providerType: ProviderType): boolean {
    return this.providerInstances.delete(providerType);
  }

  /**
   * Get provider configuration requirements
   */
  static getProviderRequirements(providerType: ProviderType): {
    requiresApiKey: boolean;
    apiKeyPattern?: RegExp;
    baseURL: string;
    description: string;
  } {
    const requirements = {
      openai: {
        requiresApiKey: true,
        apiKeyPattern: /^sk-[a-zA-Z0-9]{48}$/,
        baseURL: 'https://api.openai.com/v1',
        description: 'OpenAI GPT models (GPT-4, GPT-3.5-turbo)',
      },
      deepseek: {
        requiresApiKey: true,
        apiKeyPattern: /^sk-[a-zA-Z0-9]{32,}$/,
        baseURL: 'https://api.deepseek.com/v1',
        description: 'DeepSeek AI models (Chat, Coder)',
      },
      anthropic: {
        requiresApiKey: true,
        apiKeyPattern: /^sk-ant-[a-zA-Z0-9\-_]{95}$/,
        baseURL: 'https://api.anthropic.com/v1',
        description: 'Anthropic Claude models (Opus, Sonnet)',
      },
      local: {
        requiresApiKey: false,
        baseURL: 'http://localhost:8080/v1',
        description: 'Local AI model server (Ollama, LM Studio, etc.)',
      },
    };

    return requirements[providerType];
  }

  /**
   * Test provider connectivity without creating instance
   */
  static async testProviderConnectivity(
    providerType: ProviderType, 
    apiKey?: string, 
    baseURL?: string
  ): Promise<{ success: boolean; error?: string; latency?: number }> {
    const startTime = Date.now();
    
    try {
      // Create temporary provider instance
      let tempProvider: AIProvider;
      const config = {
        apiKey: apiKey || AIConfig.getAPIKey(providerType) || '',
        baseURL: baseURL || AIConfig.getProviderURL(providerType),
      };

      switch (providerType) {
        case 'openai':
          tempProvider = new OpenAIProvider(config);
          break;
        case 'deepseek':
          tempProvider = new DeepSeekProvider(config);
          break;
        case 'anthropic':
          tempProvider = new AnthropicProvider(config);
          break;
        case 'local':
          tempProvider = new LocalAIProvider({ baseURL: config.baseURL });
          break;
        default:
          throw new Error(`Unsupported provider: ${providerType}`);
      }

      // Test connectivity
      const isHealthy = await tempProvider.healthCheck();
      const latency = Date.now() - startTime;

      return {
        success: isHealthy,
        latency,
        error: isHealthy ? undefined : 'Health check failed',
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        latency: Date.now() - startTime,
      };
    }
  }

  /**
   * Get recommended provider based on context
   */
  static getRecommendedProvider(context: string): ProviderType {
    const recommendations: Record<string, ProviderType> = {
      planner: 'openai',     // GPT-4 for complex planning
      health: 'anthropic',   // Claude for sensitive health data
      learning: 'openai',    // GPT-4 for educational content
      chat: 'deepseek',      // Fast and cost-effective for chat
      general: 'openai',     // Default to OpenAI
    };

    return recommendations[context] || 'openai';
  }

  /**
   * Get provider capabilities
   */
  static getProviderCapabilities(providerType: ProviderType): {
    streaming: boolean;
    maxTokens: number;
    supportedModels: string[];
    specialties: string[];
  } {
    const capabilities = {
      openai: {
        streaming: true,
        maxTokens: 8192,
        supportedModels: ['gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo'],
        specialties: ['general', 'coding', 'analysis', 'creative'],
      },
      deepseek: {
        streaming: true,
        maxTokens: 4096,
        supportedModels: ['deepseek-chat', 'deepseek-coder'],
        specialties: ['coding', 'chat', 'cost-effective'],
      },
      anthropic: {
        streaming: true,
        maxTokens: 4096,
        supportedModels: ['claude-3-opus', 'claude-3-sonnet'],
        specialties: ['safety', 'analysis', 'reasoning', 'ethics'],
      },
      local: {
        streaming: false,
        maxTokens: 2048,
        supportedModels: ['local-model'],
        specialties: ['privacy', 'offline', 'custom'],
      },
    };

    return capabilities[providerType];
  }
}
