/**
 * Camera & AR Feature Module
 * 
 * التكامل مع الكاميرا والواقع المعزز مع الذكاء الاصطناعي
 * - تحليل الصور بالذكاء الاصطناعي
 * - التعرف على الكائنات
 * - الواقع المعزز التفاعلي
 * - مسح المستندات
 * - ترجمة النصوص المرئية
 */

// Domain Layer Exports
export * from './domain/entities';
export * from './domain/usecases';
export type { 
  CameraRepository, 
  ImageAnalysisRepository, 
  ARRepository,
  DocumentScanRepository 
} from './domain/repositories';

// Data Layer Exports
export { CameraRepositoryImpl } from './data/repositories/CameraRepositoryImpl';
export { ImageAnalysisRepositoryImpl } from './data/repositories/ImageAnalysisRepositoryImpl';
export { ARRepositoryImpl } from './data/repositories/ARRepositoryImpl';

// Presentation Layer Exports
export { default as CameraScreen } from './presentation/screens/CameraScreen';
export { default as ImageAnalysisScreen } from './presentation/screens/ImageAnalysisScreen';
export { default as ARViewScreen } from './presentation/screens/ARViewScreen';
export { default as DocumentScannerScreen } from './presentation/screens/DocumentScannerScreen';
export { default as TextRecognitionScreen } from './presentation/screens/TextRecognitionScreen';

// Components
export { default as CameraView } from './presentation/components/CameraView';
export { default as ImageAnalysisResults } from './presentation/components/ImageAnalysisResults';
export { default as AROverlay } from './presentation/components/AROverlay';
export { default as ObjectDetectionOverlay } from './presentation/components/ObjectDetectionOverlay';
export { default as DocumentScanner } from './presentation/components/DocumentScanner';
export { default as TextExtractor } from './presentation/components/TextExtractor';
export { default as QRCodeScanner } from './presentation/components/QRCodeScanner';

// Hooks
export { useCamera } from './presentation/hooks/useCamera';
export { useImageAnalysis } from './presentation/hooks/useImageAnalysis';
export { useARSession } from './presentation/hooks/useARSession';
export { useObjectDetection } from './presentation/hooks/useObjectDetection';
export { useDocumentScanning } from './presentation/hooks/useDocumentScanning';
export { useTextRecognition } from './presentation/hooks/useTextRecognition';

// Store
export { cameraARSlice, cameraARActions } from './presentation/store/cameraARSlice';
export type { CameraARState } from './presentation/store/cameraARSlice';

// Infrastructure
export { AIVisionService } from './infrastructure/services/AIVisionService';
export { AREngineService } from './infrastructure/services/AREngineService';
export { OCRService } from './infrastructure/services/OCRService';
export { ObjectDetectionService } from './infrastructure/services/ObjectDetectionService';

// Types
export type {
  CameraConfig,
  ImageAnalysisResult,
  DetectedObject,
  ARScene,
  ARMarker,
  DocumentScanResult,
  TextRecognitionResult,
  QRCodeResult,
  CameraPermission,
  ImageMetadata,
} from './domain/entities';

// Constants
export { CAMERA_AR_ROUTES } from './infrastructure/config/routes';
export { CAMERA_MODES, AR_FEATURES } from './infrastructure/config/constants';

/**
 * Feature Module Configuration
 */
export const CameraARModule = {
  name: 'CameraAR',
  version: '1.0.0',
  description: 'AI-powered camera and augmented reality features',
  dependencies: ['ai', 'vision', 'ar-engine'],
  permissions: ['camera', 'storage', 'location'],
  routes: [
    'CameraScreen',
    'ImageAnalysisScreen',
    'ARViewScreen',
    'DocumentScannerScreen',
    'TextRecognitionScreen',
  ],
} as const;
