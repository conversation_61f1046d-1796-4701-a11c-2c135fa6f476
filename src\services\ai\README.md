# AI Service Module - وحدة خدمات الذكاء الاصطناعي

## 🤖 نظرة عامة

وحدة شاملة ومتقدمة لخدمات الذكاء الاصطناعي مع دعم متعدد المقدمين وحماية كاملة لـ API Keys وفصل تام عن واجهات المستخدم.

## 🏗️ البنية المعمارية

```
src/services/ai/
├── core/                    # النواة الأساسية
│   ├── AIService.ts        # الخدمة الرئيسية
│   └── AIServiceFactory.ts # مصنع المقدمين
├── providers/              # مقدمو الخدمة
│   ├── OpenAIProvider.ts   # مقدم OpenAI
│   ├── DeepSeekProvider.ts # مقدم DeepSeek
│   ├── AnthropicProvider.ts# مقدم Anthropic
│   └── LocalAIProvider.ts  # مقدم محلي
├── contexts/               # خدمات متخصصة
│   ├── PlannerAIService.ts # ذكاء إدارة المهام
│   ├── HealthAIService.ts  # ذكاء صحي
│   ├── LearningAIService.ts# ذكاء تعليمي
│   └── ChatAIService.ts    # ذكاء المحادثة
├── utils/                  # أدوات مساعدة
│   ├── PromptTemplateService.ts # إدارة القوالب
│   ├── ResponseFormatterService.ts # تنسيق الردود
│   ├── CacheManager.ts     # إدارة التخزين المؤقت
│   └── RateLimiter.ts      # تحديد المعدل
├── config/                 # التكوينات
│   └── AIConfig.ts         # تكوين آمن
├── types/                  # تعريفات الأنواع
│   └── index.ts
├── constants/              # الثوابت
│   └── index.ts
└── index.ts               # نقطة الدخول الرئيسية
```

## 🚀 الاستخدام السريع

### 1. التهيئة الأولية

```typescript
import { initializeAIServices, AIConfig } from '@/services/ai';

// تعيين API Keys بشكل آمن
await AIConfig.setAPIKey('openai', 'sk-your-openai-key');
await AIConfig.setAPIKey('deepseek', 'sk-your-deepseek-key');

// تهيئة الخدمات
await initializeAIServices();
```

### 2. استخدام الدوال المتخصصة

#### Smart Planner AI
```typescript
import { askPlannerAI } from '@/services/ai';

// تحسين الجدولة
const optimizedSchedule = await askPlannerAI.optimizeSchedule({
  tasks: [
    { id: '1', title: 'Meeting with team', duration: 60, priority: 'high' },
    { id: '2', title: 'Code review', duration: 30, priority: 'medium' },
  ],
  preferences: { workingHours: '9-17', breakDuration: 15 },
});

// اقتراح مهام جديدة
const suggestedTasks = await askPlannerAI.suggestTasks({
  goals: ['Complete project', 'Learn new technology'],
  timeframe: 'this week',
});

// تحليل الأولويات
const priorityAnalysis = await askPlannerAI.analyzePriorities({
  tasks: tasks,
  deadlines: deadlines,
  goals: ['Productivity', 'Quality'],
});
```

#### Health AI
```typescript
import { askHealthAI } from '@/services/ai';

// تحليل الأعراض
const symptomAnalysis = await askHealthAI.analyzeSymptoms({
  symptoms: ['headache', 'fatigue', 'dizziness'],
  duration: '2 days',
  severity: 'moderate',
});

// نصائح صحية
const healthTips = await askHealthAI.suggestHealthTips({
  profile: { age: 30, gender: 'male', activity: 'moderate' },
  goals: ['weight loss', 'better sleep'],
});

// تفسير المؤشرات الصحية
const metricsInterpretation = await askHealthAI.interpretMetrics({
  heartRate: 75,
  bloodPressure: { systolic: 120, diastolic: 80 },
  steps: 8000,
});
```

#### Learning AI
```typescript
import { askLearningAI } from '@/services/ai';

// إنشاء مسار تعليمي
const learningPath = await askLearningAI.createLearningPath({
  skills: ['React', 'TypeScript', 'Node.js'],
  currentLevel: 'intermediate',
  goals: ['Full-stack development'],
  timeframe: '3 months',
});

// تقييم المهارات
const skillAssessment = await askLearningAI.assessSkills({
  subject: 'JavaScript',
  questions: userAnswers,
  difficulty: 'intermediate',
});

// توصيات المحتوى
const contentRecommendations = await askLearningAI.recommendContent({
  interests: ['AI', 'Mobile Development'],
  learningStyle: 'visual',
  availableTime: '2 hours/day',
});
```

#### Chat AI
```typescript
import { chatAI } from '@/services/ai';

// إرسال رسالة عادية
const response = await chatAI.sendMessage({
  message: 'Hello, how can you help me today?',
  personality: 'friendly',
  options: { style: 'casual', includeEmoji: true },
});

// إرسال رسالة مع streaming
const streamGenerator = chatAI.sendStreamingMessage({
  message: 'Tell me about artificial intelligence',
  conversationId: 'conv_123',
  personality: 'professional',
});

for await (const chunk of streamGenerator) {
  console.log(chunk.delta); // عرض النص تدريجياً
  if (chunk.isComplete) break;
}

// تحليل السياق
const contextAnalysis = await chatAI.analyzeContext({
  conversationId: 'conv_123',
});

// تغيير الشخصية
await chatAI.switchPersonality({
  conversationId: 'conv_123',
  newPersonality: 'wise',
});
```

## 🔐 الأمان وحماية API Keys

### تعيين API Keys بشكل آمن
```typescript
import { AIConfig } from '@/services/ai';

// تعيين مفتاح API
await AIConfig.setAPIKey('openai', 'sk-your-key-here');

// التحقق من وجود المفتاح
const hasKey = AIConfig.isProviderConfigured('openai');

// الحصول على مفاتيح مقنعة للعرض
const maskedKeys = AIConfig.getMaskedAPIKeys();
// { openai: 'sk-12...34', deepseek: 'Not set' }
```

### التحقق من صحة المفاتيح
```typescript
// التحقق من صحة تنسيق المفتاح
const isValidFormat = keyManager.validateAPIKeyFormat('openai', apiKey);

// اختبار الاتصال
const connectivityTest = await AIServiceFactory.testProviderConnectivity(
  'openai', 
  apiKey
);
```

## ⚙️ التكوين المتقدم

### تخصيص إعدادات الخدمة
```typescript
import { AIConfig } from '@/services/ai';

// تحديث التكوين
AIConfig.updateServiceConfig({
  defaultProvider: 'deepseek',
  fallbackProviders: ['openai', 'anthropic'],
  retryAttempts: 5,
  timeout: 45000,
  caching: {
    enabled: true,
    ttl: 600000, // 10 minutes
    maxSize: 200,
  },
  rateLimiting: {
    enabled: true,
    requestsPerMinute: 100,
  },
});
```

### تبديل المقدمين
```typescript
import { AIService } from '@/services/ai';

const aiService = AIService.getInstance();

// التبديل إلى مقدم مختلف
await aiService.switchProvider('deepseek');

// الحصول على المقدم الحالي
const currentProvider = aiService.getCurrentProvider();

// الحصول على المقدمين المتاحين
const availableProviders = aiService.getAvailableProviders();
```

## 📊 المراقبة والتحليلات

### فحص صحة الخدمات
```typescript
import { checkAIServicesHealth } from '@/services/ai';

// فحص صحة جميع الخدمات
const healthStatus = await checkAIServicesHealth();
// { planner: true, health: true, learning: true, chat: true }

// فحص صحة المقدمين
const aiService = AIService.getInstance();
const providerHealth = await aiService.healthCheck();
// { openai: true, deepseek: false, anthropic: true }
```

### إحصائيات الاستخدام
```typescript
const aiService = AIService.getInstance();
const stats = aiService.getStatistics();

console.log(stats);
// {
//   currentProvider: 'openai',
//   availableProviders: ['openai', 'deepseek'],
//   cacheStats: { hits: 45, misses: 12, size: 57 },
//   rateLimitStats: { remaining: 55, resetTime: '...' }
// }
```

## 🛠️ إدارة القوالب (Prompts)

### إضافة قالب جديد
```typescript
import { PromptTemplateService } from '@/services/ai';

const promptService = PromptTemplateService.getInstance();

await promptService.addTemplate({
  id: 'custom_task_analysis',
  name: 'Custom Task Analysis',
  context: 'planner',
  systemPrompt: 'You are an expert task analyzer...',
  userPromptTemplate: 'Analyze these tasks: {tasks}\nGoals: {goals}',
  variables: ['tasks', 'goals'],
});
```

### استخدام القوالب
```typescript
// الحصول على قالب منسق
const prompt = await promptService.getPrompt('custom_task_analysis', {
  tasks: JSON.stringify(userTasks),
  goals: 'Increase productivity',
});

console.log(prompt.systemPrompt);
console.log(prompt.userPrompt);
```

## 🔄 معالجة الأخطاء

### التعامل مع أخطاء المقدمين
```typescript
import { askPlannerAI } from '@/services/ai';

try {
  const result = await askPlannerAI.optimizeSchedule(request);
} catch (error) {
  if (error.code === 'RATE_LIMIT_EXCEEDED') {
    // التعامل مع تجاوز الحد المسموح
    console.log('Rate limit exceeded, trying again later...');
  } else if (error.code === 'ALL_PROVIDERS_FAILED') {
    // جميع المقدمين فشلوا
    console.log('All AI providers are unavailable');
  } else {
    // خطأ عام
    console.error('AI service error:', error.message);
  }
}
```

### إعادة المحاولة التلقائية
```typescript
// الخدمة تتعامل مع إعادة المحاولة تلقائياً
// مع التبديل إلى مقدمين بديلين عند الحاجة
```

## 📱 التكامل مع الميزات

### في مكونات React Native
```typescript
import React, { useState } from 'react';
import { askPlannerAI } from '@/services/ai';

const TaskPlannerScreen = () => {
  const [loading, setLoading] = useState(false);
  const [optimizedSchedule, setOptimizedSchedule] = useState(null);

  const optimizeMySchedule = async () => {
    setLoading(true);
    try {
      const result = await askPlannerAI.optimizeSchedule({
        tasks: userTasks,
        preferences: userPreferences,
      });
      setOptimizedSchedule(result.result.optimizedSchedule);
    } catch (error) {
      console.error('Failed to optimize schedule:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    // UI components...
  );
};
```

### مع Redux/State Management
```typescript
import { createAsyncThunk } from '@reduxjs/toolkit';
import { askHealthAI } from '@/services/ai';

export const analyzeSymptoms = createAsyncThunk(
  'health/analyzeSymptoms',
  async (symptoms: string[]) => {
    const response = await askHealthAI.analyzeSymptoms({
      symptoms,
      options: { includeDisclaimer: true },
    });
    return response.result;
  }
);
```

## 🧪 الاختبار

### اختبار الوحدة
```typescript
import { AIServiceFactory } from '@/services/ai';

describe('AI Service', () => {
  beforeEach(async () => {
    // إعداد مفاتيح اختبار
    await AIConfig.setAPIKey('openai', 'test-key');
  });

  it('should create OpenAI provider', async () => {
    const provider = await AIServiceFactory.createProvider('openai');
    expect(provider.name).toBe('openai');
  });

  it('should handle API errors gracefully', async () => {
    // اختبار معالجة الأخطاء
  });
});
```

## 📋 قائمة المراجعة للإعداد

- [ ] تثبيت التبعيات المطلوبة
- [ ] تعيين API Keys للمقدمين المطلوبين
- [ ] تهيئة الخدمات في التطبيق
- [ ] اختبار الاتصال مع المقدمين
- [ ] تكوين إعدادات الأمان
- [ ] إعداد المراقبة والتحليلات
- [ ] اختبار الوظائف المتخصصة
- [ ] تكوين معالجة الأخطاء

## 🔮 الميزات المستقبلية

- [ ] دعم مقدمين إضافيين (Google AI, Cohere)
- [ ] تحسين خوارزميات التخزين المؤقت
- [ ] إضافة تحليلات متقدمة للاستخدام
- [ ] دعم النماذج المحلية المتقدمة
- [ ] تحسين أداء الـ streaming
- [ ] إضافة ميزات الأمان المتقدمة

---

هذه الوحدة توفر أساساً قوياً ومرناً لجميع احتياجات الذكاء الاصطناعي في تطبيق LifeAI Assistant مع ضمان الأمان والأداء العالي! 🚀
