/**
 * AI Module - Artificial Intelligence Core
 * 
 * This module contains all AI-related functionality:
 * - AI model integrations and management
 * - Conversation prompts and templates
 * - Data processing and analysis
 * - Context management
 * - Response generation and streaming
 * - Learning and adaptation algorithms
 */

// AI Models
export { ChatModel } from './models/ChatModel';
export { VoiceModel } from './models/VoiceModel';
export { VisionModel } from './models/VisionModel';
export { EmbeddingModel } from './models/EmbeddingModel';

// Prompt Management
export { PromptManager } from './prompts/PromptManager';
export { ConversationPrompts } from './prompts/ConversationPrompts';
export { SystemPrompts } from './prompts/SystemPrompts';
export { ContextPrompts } from './prompts/ContextPrompts';

// Data Processors
export { TextProcessor } from './processors/TextProcessor';
export { VoiceProcessor } from './processors/VoiceProcessor';
export { ImageProcessor } from './processors/ImageProcessor';
export { ContextProcessor } from './processors/ContextProcessor';

// AI Engine
export { AIEngine } from './engine/AIEngine';
export { ConversationEngine } from './engine/ConversationEngine';
export { LearningEngine } from './engine/LearningEngine';

// Context Management
export { ContextManager } from './context/ContextManager';
export { MemoryManager } from './context/MemoryManager';
export { PersonalizationEngine } from './context/PersonalizationEngine';

// Types
export type {
  AIModel,
  AIResponse,
  ConversationContext,
  PromptTemplate,
  ProcessingResult,
  LearningData,
  PersonalizationProfile,
} from './types';
